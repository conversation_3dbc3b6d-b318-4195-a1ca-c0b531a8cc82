//+------------------------------------------------------------------+
//| Test Source Price Enum Values                                   |
//+------------------------------------------------------------------+
#property copyright "Test"
#property version   "1.00"
#property script_show_inputs

input ENUM_APPLIED_PRICE Test_Source_Price = PRICE_MEDIAN; // Test Source Price

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== ENUM_APPLIED_PRICE Values Test ===");
    Print("PRICE_CLOSE = ", (int)PRICE_CLOSE);
    Print("PRICE_OPEN = ", (int)PRICE_OPEN);
    Print("PRICE_HIGH = ", (int)PRICE_HIGH);
    Print("PRICE_LOW = ", (int)PRICE_LOW);
    Print("PRICE_MEDIAN = ", (int)PRICE_MEDIAN);
    Print("PRICE_TYPICAL = ", (int)PRICE_TYPICAL);
    Print("PRICE_WEIGHTED = ", (int)PRICE_WEIGHTED);

    Print("\n=== EnumToString Test ===");
    Print("EnumToString(PRICE_CLOSE) = ", EnumToString(PRICE_CLOSE));
    Print("EnumToString(PRICE_OPEN) = ", EnumToString(PRICE_OPEN));
    Print("EnumToString(PRICE_HIGH) = ", EnumToString(PRICE_HIGH));
    Print("EnumToString(PRICE_LOW) = ", EnumToString(PRICE_LOW));
    Print("EnumToString(PRICE_MEDIAN) = ", EnumToString(PRICE_MEDIAN));
    Print("EnumToString(PRICE_TYPICAL) = ", EnumToString(PRICE_TYPICAL));
    Print("EnumToString(PRICE_WEIGHTED) = ", EnumToString(PRICE_WEIGHTED));

    Print("\n=== Value 4 Test ===");
    ENUM_APPLIED_PRICE test_price = (ENUM_APPLIED_PRICE)4;
    Print("(ENUM_APPLIED_PRICE)4 = ", EnumToString(test_price));
    Print("Should be PRICE_MEDIAN");

    Print("\n=== Input Parameter Test ===");
    Print("Test_Source_Price = ", (int)Test_Source_Price);
    Print("EnumToString(Test_Source_Price) = ", EnumToString(Test_Source_Price));

    // Custom string conversion like in EA
    string source_name = "";
    switch(Test_Source_Price)
    {
        case PRICE_CLOSE: source_name = "PRICE_CLOSE"; break;
        case PRICE_OPEN: source_name = "PRICE_OPEN"; break;
        case PRICE_HIGH: source_name = "PRICE_HIGH"; break;
        case PRICE_LOW: source_name = "PRICE_LOW"; break;
        case PRICE_MEDIAN: source_name = "PRICE_MEDIAN (HL2)"; break;
        case PRICE_TYPICAL: source_name = "PRICE_TYPICAL (HLC3)"; break;
        case PRICE_WEIGHTED: source_name = "PRICE_WEIGHTED (HLCC4)"; break;
        default: source_name = "UNKNOWN"; break;
    }
    Print("Custom conversion: ", source_name);
}
