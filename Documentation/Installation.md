# Supertrend EA Installation Guide

## Overview
The Supertrend Expert Advisor is a complete trading system based on the popular Supertrend indicator, featuring:
- **Automated Trading**: Based on trend reversals with Supertrend indicator
- **Manual Trading Controls**: BUY, SELL, and CLOSE ALL buttons for instant trade control
- **Historical Signal Analysis**: Shows past signals on startup to validate calculations
- **Real-time Dashboard**: Trade statistics and P&L (starts with BUY trend, scanner OFF)
- **Multi-timeframe Scanner**: Hidden by default, activated by user
- **Enhanced Signal Display**: TradingView-style signals with labels and backgrounds
- **Fixed Trailing Stop System**: Proper profit requirements and step logic
- **Professional Visualization**: Chart signals and trend highlighting

## System Requirements
- MetaTrader 5 (Build 3815 or higher)
- Windows 10/11 or compatible OS
- Minimum 4GB RAM
- Stable internet connection
- Hedging account type (recommended for multiple positions)

## Installation Steps

### 1. File Placement
Copy the following files to your MT5 data folder:

**Expert Advisor (Single File):**
- Copy `SupertrendEA.mq5` to `MQL5/Experts/` folder

**Settings File:**
- Copy `SupertrendEA.set` to `MQL5/Presets/` folder

### 2. Compilation
1. Open MetaEditor (F4 in MT5)
2. Navigate to `MQL5/Experts/SupertrendEA.mq5`
3. Click Compile (F7) - should show "0 errors, 0 warnings"

**Note**: No separate indicator file is needed - all Supertrend calculations are integrated within the EA.

### 3. EA Setup
1. In MT5, open the desired chart (EURUSD recommended for testing)
2. Go to Navigator → Expert Advisors
3. Drag `SupertrendEA` onto the chart
4. In the settings dialog:
   - Go to "Input Parameters" tab
   - Click "Load" and select `SupertrendEA.set`
   - Adjust settings as needed (see Configuration section)
   - Check "Allow live trading" and "Allow DLL imports"
5. Click OK

### 4. Verification
After installation, you should see:
- **Dashboard panel** in the top-left corner showing "Current Trend: BUY"
- **Historical signals** immediately displayed on chart (past BUY/SELL arrows)
- **Scanner panel** hidden by default (click "Scanner OFF" to show)
- **Manual trading buttons** (BUY, SELL, CLOSE ALL) on dashboard
- **Supertrend lines** on the chart
- **Professional signal arrows** with BUY/SELL labels when new signals occur

### 5. First Use Experience
**What you'll see immediately:**
1. Dashboard shows "Current Trend: 🟢 BUY" (default)
2. Historical buy/sell signals appear on chart
3. Scanner button shows "⚫ Scanner OFF"
4. Manual trading buttons ready for use
5. Journal shows: "Analyzed X bars and found Y signals"

## Quick Start Configuration

### Conservative Settings (Low Risk)
```
Lot_Size = 0.01
Take_Profit = 500
Stop_Loss = 300
Enable_Trailing_Stop = true
Trailing_Stop_Distance = 200
Trailing_Stop_Step = 50
Min_Profit_To_Trail = 200
ATR_Period = 14
ATR_Multiplier = 3.5
Show_Historical_Signals = true
Historical_Bars = 300
```

### Aggressive Settings (Higher Risk)
```
Lot_Size = 0.1
Take_Profit = 800
Stop_Loss = 400
Enable_Trailing_Stop = true
Trailing_Stop_Distance = 250
ATR_Period = 10
ATR_Multiplier = 2.5
```

### Scalping Settings (M1/M5 timeframes)
```
Lot_Size = 0.05
Take_Profit = 150
Stop_Loss = 100
Enable_Trailing_Stop = true
Trailing_Stop_Distance = 80
ATR_Period = 8
ATR_Multiplier = 2.0
Scanner_Timeframes = M1,M5,M15,M30
```

## Troubleshooting

### Common Issues

**EA not trading:**
- Check "Allow live trading" is enabled
- Verify account has sufficient margin
- Ensure market is open
- Check Enable_Auto_Trading parameter is true

**Dashboard/Scanner not showing:**
- Dashboard: Verify Show_Dashboard is true, check X,Y position
- Scanner: Scanner is OFF by default - click "Scanner OFF" button to show
- Check position parameters (may be off-screen)
- Restart EA (remove and re-attach to chart)

**Manual trading buttons not working:**
- Verify dashboard is visible and buttons are clickable
- Check account balance and margin requirements
- Ensure current EA settings are valid (lot size, TP, SL)
- Look for error messages in MT5 Journal

**No historical signals appearing:**
- Check Show_Historical_Signals = true
- Verify Show_Buy_Sell_Signals = true
- Ensure sufficient historical data available
- Check Historical_Bars setting (default: 500)

**Trailing stop not working:**
- Check Enable_Trailing_Stop = true
- Verify position is profitable enough (Min_Profit_To_Trail)
- Ensure price has moved enough (Trailing_Stop_Step)
- Check journal for trailing stop update messages

**Compilation errors:**
- Ensure all files are in correct folders
- Check MT5 build version compatibility
- Verify no syntax errors in code

**Signals not appearing:**
- Check Show_Buy_Sell_Signals parameter
- Verify Supertrend calculation is working
- Ensure sufficient historical data

### Performance Optimization

**For VPS/Server:**
- Reduce Scanner_Update_Seconds to 10-15
- Disable chart visualization if not needed
- Use smaller Dashboard/Scanner sizes

**For Multiple Charts:**
- Use different Magic_Numbers for each chart
- Adjust Dashboard/Scanner positions to avoid overlap
- Consider disabling scanner on secondary charts

## Support and Updates

For technical support or updates:
1. Check the MT5 Journal tab for error messages
2. Verify all input parameters are within valid ranges
3. Test on demo account before live trading
4. Keep MT5 platform updated to latest version

## Important Notes

⚠️ **Risk Warning:** Trading involves substantial risk of loss. Test thoroughly on demo account before live trading.

⚠️ **Broker Compatibility:** Ensure your broker supports hedging if Allow_Hedging is enabled.

⚠️ **Timeframe Considerations:** EA works on all timeframes but performance varies. M15-H1 recommended for beginners.

⚠️ **News Events:** Consider disabling auto-trading during high-impact news releases.

## Next Steps
After successful installation, refer to the User Guide for detailed parameter explanations and trading strategies.
