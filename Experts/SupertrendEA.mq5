//+------------------------------------------------------------------+
//|                                                SupertrendEA.mq5 |
//|                                  Copyright 2024, Supertrend EA  |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Supertrend EA"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Supertrend Expert Advisor with Dashboard and Multi-Timeframe Scanner"

//--- Include files
#include <Trade\Trade.mqh>
#include <Controls\Dialog.mqh>
#include <Controls\Label.mqh>
#include <Controls\Panel.mqh>

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+

//--- Supertrend Settings
input group "📊 SUPERTREND SETTINGS"
input int                ATR_Period = 10;                    // ATR Period
input double             ATR_Multiplier = 3.0;              // ATR Multiplier
input bool               Use_Smoothed_ATR = true;           // Use Smoothed ATR (true=ATR, false=SMA)
input ENUM_APPLIED_PRICE Source_Price = PRICE_MEDIAN;       // Source Price (HL2)
input bool               Wait_For_Candle_Close = true;      // Wait for Candle Close Confirmation
input bool               Enable_Trend_Reversal_Check = true; // Enable Trend Reversal Validation
input int                Confirmation_Bars = 1;             // Bars to Confirm Trend Change (1-3)
input bool               Show_Signals_Immediately = true;   // Show Signals Immediately (like TradingView)

//--- Trading Settings
input group "💰 TRADING SETTINGS"
input double             Lot_Size = 0.1;                    // Lot Size
input int                Take_Profit = 500;                 // Take Profit (points)
input int                Stop_Loss = 300;                   // Stop Loss (points)
input bool               Enable_Trailing_Stop = false;      // Enable Trailing Stop
input int                Trailing_Stop_Distance = 200;      // Trailing Stop Distance (points)
input int                Trailing_Stop_Step = 50;           // Trailing Stop Step (points)
input int                Min_Profit_To_Trail = 200;         // Minimum Profit to Start Trailing (points)
input int                Magic_Number = 12345;              // Magic Number
input string             Trade_Comment = "Supertrend EA";   // Trade Comment
input bool               Enable_Auto_Trading = true;        // Enable Auto Trading
input bool               Allow_Hedging = true;              // Allow Multiple Positions

//--- Trading Time Settings
input group "⏰ TRADING TIME SETTINGS"
input bool               Enable_Trading_Hours = false;      // Enable Trading Hours Filter
input string             Monday_Start = "00:00";            // Monday Start Time (HH:MM)
input string             Monday_End = "23:59";              // Monday End Time (HH:MM)
input string             Tuesday_Start = "00:00";           // Tuesday Start Time (HH:MM)
input string             Tuesday_End = "23:59";             // Tuesday End Time (HH:MM)
input string             Wednesday_Start = "00:00";         // Wednesday Start Time (HH:MM)
input string             Wednesday_End = "23:59";           // Wednesday End Time (HH:MM)
input string             Thursday_Start = "00:00";          // Thursday Start Time (HH:MM)
input string             Thursday_End = "23:59";            // Thursday End Time (HH:MM)
input string             Friday_Start = "00:00";            // Friday Start Time (HH:MM)
input string             Friday_End = "23:59";              // Friday End Time (HH:MM)
input string             Saturday_Start = "00:00";          // Saturday Start Time (HH:MM)
input string             Saturday_End = "23:59";            // Saturday End Time (HH:MM)
input string             Sunday_Start = "00:00";            // Sunday Start Time (HH:MM)
input string             Sunday_End = "23:59";              // Sunday End Time (HH:MM)
input bool               Use_Broker_Time = true;            // Use Broker Time (false = GMT)
input bool               Close_Trades_Outside_Hours = false; // Close All Trades Outside Trading Hours

//--- Dashboard Settings
input group "📋 DASHBOARD SETTINGS"
input bool               Show_Dashboard = true;             // Show Dashboard
input int                Dashboard_X = 20;                  // Dashboard X Position
input int                Dashboard_Y = 50;                  // Dashboard Y Position
input int                Dashboard_Width = 280;             // Dashboard Width
input int                Dashboard_Height = 300;            // Dashboard Height
input color              Dashboard_Background = C'240,240,240'; // Dashboard Background Color
input color              Dashboard_Border = clrDarkGray;    // Dashboard Border Color
input int                Dashboard_Font_Size = 9;           // Dashboard Font Size

//--- Scanner Settings
input group "🔍 MULTI-TIMEFRAME SCANNER SETTINGS"
input bool               Enable_Scanner = true;             // Enable Scanner
input string             Scanner_Timeframes = "M1,M5,M15,M20,M30,H1,H2,H3,H4,H8,H12,D1,W1"; // Timeframes (comma separated)
input int                Scanner_X = 632;                   // Scanner X Position (from right)
input int                Scanner_Y = 40;                    // Scanner Y Position
input int                Scanner_Width = 617;               // Scanner Width
input int                Scanner_Height = 374;              // Scanner Height
input int                Scanner_Update_Seconds = 5;        // Update Frequency (seconds)
input color              Scanner_Background = C'30,30,30';  // Scanner Background Color
input color              Scanner_Header_Color = C'60,60,60'; // Scanner Header Color
input int                Scanner_Font_Size = 11;            // Scanner Font Size

//--- Scanner Indicator Periods
input group "📈 SCANNER INDICATOR PERIODS"
input int                RSI_Period = 14;                   // RSI Period
input int                Stochastic_K_Period = 14;          // Stochastic %K Period
input int                Stochastic_D_Period = 3;           // Stochastic %D Period
input int                Stochastic_Slowing = 3;            // Stochastic Slowing
input int                CCI_Period = 20;                   // CCI Period
input int                ADX_Period = 14;                   // ADX Period

//--- Scanner Indicator Thresholds
input group "🎯 SCANNER INDICATOR THRESHOLDS"
input double             RSI_Oversold_Level = 30.0;         // RSI Oversold Level
input double             RSI_Overbought_Level = 70.0;       // RSI Overbought Level
input double             Stoch_Oversold_Level = 20.0;       // Stochastic Oversold Level
input double             Stoch_Overbought_Level = 80.0;     // Stochastic Overbought Level
input double             CCI_Oversold_Level = -100.0;       // CCI Oversold Level
input double             CCI_Overbought_Level = 100.0;      // CCI Overbought Level

//--- Chart Visualization
input group "🎨 CHART VISUALIZATION"
input bool               Show_Buy_Sell_Signals = true;      // Show Buy/Sell Signals
input bool               Show_Signal_Labels = true;         // Show BUY/SELL Labels
input bool               Show_Signal_Background = true;     // Show Signal Background
input bool               Show_Historical_Signals = true;    // Show Historical Signals on Startup
input int                Historical_Bars = 500;             // Historical Bars to Analyze (50-1000)
input bool               Show_Trend_Lines = true;           // Show Supertrend Lines
input bool               Highlight_Trend_Zones = true;      // Highlight Trend Zones
input int                Signal_Arrow_Size = 3;             // Signal Arrow Size (1-5)
input int                Signal_Label_Size = 8;             // Signal Label Font Size
input int                Trend_Line_Width = 2;              // Trend Line Width
input color              Buy_Signal_Color = clrLime;        // Buy Signal Color
input color              Sell_Signal_Color = clrRed;        // Sell Signal Color
input int                Signal_Distance_Points = 20;       // Signal Distance from Candle (points)
input color              Bullish_Zone_Color = C'230,255,230'; // Bullish Zone Color
input color              Bearish_Zone_Color = C'255,230,230'; // Bearish Zone Color
input bool               Show_Heiken_Ashi = false;          // Show Heiken Ashi Overlay

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
CTrade trade;
int atr_handle;

//--- Supertrend calculation arrays
double supertrend_up[], supertrend_down[], trend_buffer[];
double atr_buffer[];
double up_trend[], down_trend[];

//--- Current state variables
int current_trend = 0;
int previous_trend = 0;
datetime trend_start_time = 0;
datetime last_signal_time = 0;
int last_signal_type = 0; // 1 = Buy, -1 = Sell

//--- Candle confirmation variables
datetime last_bar_time = 0;
bool new_bar_formed = false;
int pending_trend = 0; // Trend waiting for confirmation
int confirmation_count = 0; // Bars confirmed for trend change

//--- Directional trading variables
bool Allow_Buy_Trades = true;   // Allow Buy Trades (modifiable)
bool Allow_Sell_Trades = true;  // Allow Sell Trades (modifiable)

//--- Trading time variables
struct TradingTimeSettings
{
    int start_hour;
    int start_minute;
    int end_hour;
    int end_minute;
};

TradingTimeSettings daily_trading_times[7]; // 0=Sunday, 1=Monday, ..., 6=Saturday
bool is_trading_time_allowed = true;
datetime last_time_check = 0;

//--- Signal tracking variables
datetime last_drawn_signal_time = 0;
int last_drawn_signal_type = 0; // 1 = Buy, -1 = Sell
bool trend_change_confirmed_this_bar = false; // Flag to track if trend was confirmed on current bar

//--- Trend persistence variables (prevent duplicate signals)
datetime last_trend_change_time = 0;
int last_confirmed_trend = 0; // Last confirmed trend to prevent duplicate signals
bool signal_already_generated = false; // Flag to prevent multiple signals per trend change

//--- Previous values for Supertrend calculation
double prev_up = 0;
double prev_down = 0;

//--- Dashboard objects
string dashboard_objects[];
string scanner_objects[];

//--- Scanner variables
string timeframe_list[];
ENUM_TIMEFRAMES timeframe_enums[];
int timeframe_count = 0;
bool scanner_panel_visible = false;

//--- Scanner indicator values
double scanner_rsi_values[];
double scanner_stoch_values[];
double scanner_cci_values[];
double scanner_adx_values[];
double scanner_ao_values[];

//--- Scanner UI constants
#define SCANNER_MAIN_PANEL          "SCANNER_PANEL_MAIN"
#define SCANNER_HEADER_PANEL        "SCANNER_PANEL_HEADER"
#define SCANNER_HEADER_ICON         "SCANNER_PANEL_HEADER_ICON"
#define SCANNER_HEADER_TEXT         "SCANNER_PANEL_HEADER_TEXT"
#define SCANNER_CLOSE_BUTTON        "SCANNER_BUTTON_CLOSE"
#define SCANNER_SYMBOL_RECTANGLE    "SCANNER_SYMBOL_HEADER"
#define SCANNER_SYMBOL_TEXT         "SCANNER_SYMBOL_TEXT"
#define SCANNER_TIMEFRAME_RECTANGLE "SCANNER_TIMEFRAME_"
#define SCANNER_TIMEFRAME_TEXT      "SCANNER_TIMEFRAME_TEXT_"
#define SCANNER_HEADER_RECTANGLE    "SCANNER_HEADER_"
#define SCANNER_HEADER_TEXT         "SCANNER_HEADER_TEXT_"
#define SCANNER_RSI_RECTANGLE       "SCANNER_RSI_"
#define SCANNER_RSI_TEXT            "SCANNER_RSI_TEXT_"
#define SCANNER_STOCH_RECTANGLE     "SCANNER_STOCH_"
#define SCANNER_STOCH_TEXT          "SCANNER_STOCH_TEXT_"
#define SCANNER_CCI_RECTANGLE       "SCANNER_CCI_"
#define SCANNER_CCI_TEXT            "SCANNER_CCI_TEXT_"
#define SCANNER_ADX_RECTANGLE       "SCANNER_ADX_"
#define SCANNER_ADX_TEXT            "SCANNER_ADX_TEXT_"
#define SCANNER_AO_RECTANGLE        "SCANNER_AO_"
#define SCANNER_AO_TEXT             "SCANNER_AO_TEXT_"
#define SCANNER_BUY_RECTANGLE       "SCANNER_BUY_"
#define SCANNER_BUY_TEXT            "SCANNER_BUY_TEXT_"
#define SCANNER_SELL_RECTANGLE      "SCANNER_SELL_"
#define SCANNER_SELL_TEXT           "SCANNER_SELL_TEXT_"

//--- Scanner dimensions
#define SCANNER_WIDTH_TIMEFRAME     90
#define SCANNER_WIDTH_INDICATOR     70
#define SCANNER_WIDTH_SIGNAL        90
#define SCANNER_HEIGHT_RECTANGLE    25

//--- Timer variables
datetime last_update_time = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Initialize trade object
    trade.SetExpertMagicNumber(Magic_Number);
    trade.SetMarginMode();
    trade.SetTypeFillingBySymbol(Symbol());
    
    //--- Create ATR indicator for Supertrend calculation
    if(!InitializeSupertrendCalculation())
    {
        Print("Failed to initialize Supertrend calculation");
        return INIT_FAILED;
    }
    
    //--- Parse timeframes for scanner
    if(Enable_Scanner)
    {
        ParseTimeframes();
        InitializeScannerArrays();
    }
    
    //--- Create dashboard
    if(Show_Dashboard)
    {
        CreateDashboard();
    }
    
    //--- Create scanner
    if(Enable_Scanner && scanner_panel_visible)
    {
        CreateScanner();
    }
    
    //--- Set timer for updates
    EventSetTimer(Scanner_Update_Seconds);
    
    //--- Initialize trend tracking
    trend_start_time = TimeCurrent();

    //--- Initialize trading time settings
    if(Enable_Trading_Hours)
    {
        InitializeTradingTimeSettings();
    }

    //--- Validate trailing stop parameters
    if(Enable_Trailing_Stop)
    {
        ValidateTrailingStopParameters();
    }

    //--- Clean up any existing signal objects
    CleanupSignalObjects();

    //--- Reset signal tracking variables
    last_drawn_signal_time = 0;
    last_drawn_signal_type = 0;
    trend_change_confirmed_this_bar = false;

    //--- Reset trend persistence variables
    last_trend_change_time = 0;
    last_confirmed_trend = 0;
    signal_already_generated = false;

    //--- Debug: Show loaded Source_Price value
    string source_debug = "";
    switch(Source_Price)
    {
        case PRICE_CLOSE: source_debug = "PRICE_CLOSE"; break;
        case PRICE_OPEN: source_debug = "PRICE_OPEN"; break;
        case PRICE_HIGH: source_debug = "PRICE_HIGH"; break;
        case PRICE_LOW: source_debug = "PRICE_LOW"; break;
        case PRICE_MEDIAN: source_debug = "PRICE_MEDIAN (HL2)"; break;
        case PRICE_TYPICAL: source_debug = "PRICE_TYPICAL (HLC3)"; break;
        case PRICE_WEIGHTED: source_debug = "PRICE_WEIGHTED (HLCC4)"; break;
        default: source_debug = "UNKNOWN"; break;
    }
    Print("📊 Loaded Source_Price: ", source_debug, " (", (int)Source_Price, ")");
    Print("🔧 TradingView Sync Fix Applied: Band smoothing and trend logic updated");

    //--- Calculate and display historical signals
    if(Show_Buy_Sell_Signals && Show_Historical_Signals)
    {
        CalculateHistoricalSignals();
    }

    //--- Initialize trend state properly to match current market conditions
    InitializeTrendState();

    Print("🚀 Supertrend EA v2.1 (TradingView Sync) initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Initialize Trend State to Match Current Market Conditions       |
//+------------------------------------------------------------------+
void InitializeTrendState()
{
    // CRITICAL FIX: Calculate trend state from historical data to match TradingView exactly
    // This ensures consistent trend state on EA restart

    // Get enough historical data to calculate proper trend state
    double high[], low[], close[], open[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(open, true);

    int bars_needed = ATR_Period + 50; // Extra bars for accurate calculation

    if(CopyHigh(Symbol(), PERIOD_CURRENT, 0, bars_needed, high) < bars_needed ||
       CopyLow(Symbol(), PERIOD_CURRENT, 0, bars_needed, low) < bars_needed ||
       CopyClose(Symbol(), PERIOD_CURRENT, 0, bars_needed, close) < bars_needed ||
       CopyOpen(Symbol(), PERIOD_CURRENT, 0, bars_needed, open) < bars_needed)
    {
        Print("Failed to copy historical data for trend initialization");
        // Fallback to simple calculation
        if(!CalculateSupertrendValues())
        {
            Print("Failed to calculate initial Supertrend values for trend state initialization");
            return;
        }
        last_confirmed_trend = current_trend;
        Print("🔄 Trend state initialized (fallback): Current trend = ", (current_trend == 1 ? "BULLISH" : "BEARISH"));
        return;
    }

    // Calculate historical Supertrend to determine ACTUAL current trend state
    // This matches TradingView's approach of maintaining trend continuity

    // Calculate ATR for trend determination
    double atr_for_init = 0;
    if(Use_Smoothed_ATR)
    {
        // Calculate Wilder's smoothed ATR
        double tr_sum = 0;
        for(int i = 0; i < ATR_Period; i++)
        {
            double tr1 = high[i] - low[i];
            double tr2 = (i < ArraySize(close) - 1) ? MathAbs(high[i] - close[i+1]) : tr1;
            double tr3 = (i < ArraySize(close) - 1) ? MathAbs(low[i] - close[i+1]) : tr1;
            tr_sum += MathMax(tr1, MathMax(tr2, tr3));
        }
        atr_for_init = tr_sum / ATR_Period;
    }
    else
    {
        // Calculate SMA of True Range
        double tr_sum = 0;
        for(int i = 0; i < ATR_Period; i++)
        {
            double tr1 = high[i] - low[i];
            double tr2 = MathAbs(high[i] - close[i+1]);
            double tr3 = MathAbs(low[i] - close[i+1]);
            tr_sum += MathMax(tr1, MathMax(tr2, tr3));
        }
        atr_for_init = tr_sum / ATR_Period;
    }

    // Calculate current Supertrend bands
    double src_init = (high[0] + low[0]) / 2.0; // Use HL2 for initialization
    double up_init = src_init - (ATR_Multiplier * atr_for_init);
    double dn_init = src_init + (ATR_Multiplier * atr_for_init);

    // Determine trend based on price position relative to Supertrend bands
    double current_close = close[0];

    if(current_close > up_init)
    {
        current_trend = 1; // Bullish
        prev_up = up_init;
        prev_down = dn_init;
        Print("🔄 Trend state initialized: BULLISH (price above Supertrend) | Price: ", current_close, " > Up: ", up_init);
    }
    else if(current_close < dn_init)
    {
        current_trend = -1; // Bearish
        prev_up = up_init;
        prev_down = dn_init;
        Print("🔄 Trend state initialized: BEARISH (price below Supertrend) | Price: ", current_close, " < Down: ", dn_init);
    }
    else
    {
        // Price between bands - use previous trend or default to bullish
        current_trend = 1; // Default to bullish like TradingView
        prev_up = up_init;
        prev_down = dn_init;
        Print("🔄 Trend state initialized: BULLISH (default - price between bands) | Price: ", current_close);
    }

    // Set initial trend state
    last_confirmed_trend = current_trend;
    previous_trend = current_trend; // Initialize previous trend to avoid false signals

    // Now calculate proper Supertrend values with initialized state
    if(!CalculateSupertrendValues())
    {
        Print("Failed to calculate Supertrend values after trend initialization");
        return;
    }

    Print("✅ Trend state properly initialized from market conditions: ",
          (current_trend == 1 ? "BULLISH" : "BEARISH"), " | ATR: ", atr_for_init);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Kill timer
    EventKillTimer();

    //--- Clean up dashboard objects
    DeleteDashboard();

    //--- Clean up scanner objects
    DeleteScanner();

    //--- Clean up signal objects
    CleanupSignalObjects();

    //--- Release ATR indicator handle
    if(atr_handle != INVALID_HANDLE)
        IndicatorRelease(atr_handle);

    Print("Supertrend EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                            |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check if auto trading is enabled
    if(!Enable_Auto_Trading)
        return;

    //--- Check trading time restrictions
    if(Enable_Trading_Hours)
    {
        CheckTradingTime();

        // Close trades outside trading hours if enabled
        if(!is_trading_time_allowed && Close_Trades_Outside_Hours)
        {
            CloseAllTrades();
        }

        // Don't open new trades outside trading hours
        if(!is_trading_time_allowed)
        {
            // Still update dashboard and scanner
            datetime current_bar_time = iTime(Symbol(), PERIOD_CURRENT, 0);
            if((int)current_bar_time != (int)last_bar_time)
            {
                if(Show_Dashboard)
                    UpdateDashboard();
                last_bar_time = current_bar_time;
            }
            return;
        }
    }

    //--- Check for new bar formation
    CheckNewBar();

    //--- Calculate Supertrend values ONLY on new bar formation (like TradingView)
    if(new_bar_formed)
    {
        // Store previous trend state before calculation
        int trend_before_calc = current_trend;

        if(!CalculateSupertrendValues())
            return;

        // IMPORTANT: Only check for signals if trend actually changed
        // This prevents false signals during runtime
        bool trend_actually_changed = (current_trend != trend_before_calc);

        if(trend_actually_changed)
        {
            Print("🔄 CONFIRMED Trend Change: ", trend_before_calc, " → ", current_trend,
                  " | Bar: ", TimeToString(iTime(Symbol(), PERIOD_CURRENT, 0), TIME_MINUTES));
        }

        //--- Check for trading signals ONLY after new calculation (like TradingView)
        if(Enable_Auto_Trading)
        {
        // Check directional trading permissions
        bool can_buy = Allow_Buy_Trades;
        bool can_sell = Allow_Sell_Trades;

        // If only one direction is allowed, trade only in that direction regardless of Supertrend
        if(can_buy && !can_sell)
        {
            // Only buy trades allowed - open buy even on sell signals
            if(CheckBuySignal() || CheckSellSignal())
            {
                if(Allow_Hedging || CountPositions(POSITION_TYPE_BUY) == 0)
                {
                    Print("Directional trading: Opening BUY (only buys allowed)");
                    OpenBuyOrder();
                }
            }
        }
        else if(can_sell && !can_buy)
        {
            // Only sell trades allowed - open sell even on buy signals
            if(CheckBuySignal() || CheckSellSignal())
            {
                if(Allow_Hedging || CountPositions(POSITION_TYPE_SELL) == 0)
                {
                    Print("Directional trading: Opening SELL (only sells allowed)");
                    OpenSellOrder();
                }
            }
        }
        else if(can_buy && can_sell)
        {
            // Both directions allowed - normal Supertrend trading
            if(CheckBuySignal())
            {
                if(Allow_Hedging || CountPositions(POSITION_TYPE_BUY) == 0)
                {
                    OpenBuyOrder();
                }
            }
            else if(CheckSellSignal())
            {
                if(Allow_Hedging || CountPositions(POSITION_TYPE_SELL) == 0)
                {
                    OpenSellOrder();
                }
            }
        }
        else
        {
            // Neither direction allowed - no trading
            if(CheckBuySignal() || CheckSellSignal())
            {
                Print("Trading signal detected but both directions are disabled");
            }
        }
        }
    }

    //--- Manage existing trades (always check, regardless of new bar)
    if(Enable_Trailing_Stop)
    {
        ManageTrailingStop();
    }
    
    //--- Update dashboard on new bar
    datetime current_bar_time = iTime(Symbol(), PERIOD_CURRENT, 0);
    if((int)current_bar_time != (int)last_bar_time)
    {
        if(Show_Dashboard)
            UpdateDashboard();
        last_bar_time = current_bar_time;
    }
}

//+------------------------------------------------------------------+
//| Timer function                                                  |
//+------------------------------------------------------------------+
void OnTimer()
{
    //--- Update scanner
    if(Enable_Scanner && scanner_panel_visible)
    {
        UpdateScanner();
    }

    //--- Update dashboard
    if(Show_Dashboard)
    {
        UpdateDashboard();
    }
}

//+------------------------------------------------------------------+
//| Initialize Supertrend Calculation                               |
//+------------------------------------------------------------------+
bool InitializeSupertrendCalculation()
{
    //--- Create ATR indicator - always create for reference, but we'll calculate manually to match TradingView exactly
    atr_handle = iATR(Symbol(), PERIOD_CURRENT, ATR_Period);

    if(Use_Smoothed_ATR)
    {
        Print("Using TradingView atr() equivalent (Wilder's smoothing) - Manual calculation for exact match");
    }
    else
    {
        Print("Using TradingView atr2 equivalent (SMA of True Range) - Manual calculation for exact match");
    }

    if(atr_handle == INVALID_HANDLE)
    {
        Print("Failed to create ATR indicator");
        return false;
    }

    //--- Initialize arrays
    ArraySetAsSeries(supertrend_up, true);
    ArraySetAsSeries(supertrend_down, true);
    ArraySetAsSeries(trend_buffer, true);
    ArraySetAsSeries(atr_buffer, true);
    ArraySetAsSeries(up_trend, true);
    ArraySetAsSeries(down_trend, true);

    //--- Resize arrays
    ArrayResize(supertrend_up, 1000);
    ArrayResize(supertrend_down, 1000);
    ArrayResize(trend_buffer, 1000);
    ArrayResize(atr_buffer, 1000);
    ArrayResize(up_trend, 1000);
    ArrayResize(down_trend, 1000);

    //--- Initialize values
    prev_up = 0;
    prev_down = 0;
    current_trend = 0; // Will be determined on first calculation

    return true;
}

//+------------------------------------------------------------------+
//| Calculate Supertrend Values                                     |
//+------------------------------------------------------------------+
bool CalculateSupertrendValues()
{
    //--- Get price data first
    double high[], low[], close[], open[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(open, true);

    if(CopyHigh(Symbol(), PERIOD_CURRENT, 0, ATR_Period + 2, high) < ATR_Period + 2 ||
       CopyLow(Symbol(), PERIOD_CURRENT, 0, ATR_Period + 2, low) < ATR_Period + 2 ||
       CopyClose(Symbol(), PERIOD_CURRENT, 0, ATR_Period + 2, close) < ATR_Period + 2 ||
       CopyOpen(Symbol(), PERIOD_CURRENT, 0, ATR_Period + 2, open) < ATR_Period + 2)
    {
        Print("Failed to copy price data");
        return false;
    }

    //--- Calculate ATR value exactly like TradingView (manual calculation for precision)
    double atr = 0;

    // Always calculate manually to ensure exact TradingView match
    if(Use_Smoothed_ATR)
    {
        //--- Calculate Wilder's smoothed ATR exactly like TradingView atr() function
        // TradingView: atr = changeATR ? atr(Periods) : atr2 (where changeATR=true by default)

        // Get enough data for Wilder's smoothing calculation
        double tr_values[];
        ArrayResize(tr_values, 1000);

        // Calculate True Range for each bar
        for(int i = 0; i < ATR_Period + 50; i++) // Extra bars for accurate smoothing
        {
            if(i >= ArraySize(high) - 1) break;

            double tr1 = high[i] - low[i];
            double tr2 = (i < ArraySize(close) - 1) ? MathAbs(high[i] - close[i+1]) : tr1;
            double tr3 = (i < ArraySize(close) - 1) ? MathAbs(low[i] - close[i+1]) : tr1;
            tr_values[i] = MathMax(tr1, MathMax(tr2, tr3));
        }

        // Apply Wilder's smoothing (like TradingView atr() function)
        // First ATR value is SMA of first ATR_Period values
        double first_atr = 0;
        for(int i = 0; i < ATR_Period; i++)
        {
            first_atr += tr_values[ATR_Period - 1 - i];
        }
        first_atr /= ATR_Period;

        // Apply Wilder's smoothing: ATR = (Previous_ATR * (n-1) + Current_TR) / n
        atr = first_atr;
        for(int i = ATR_Period; i > 0; i--)
        {
            atr = (atr * (ATR_Period - 1) + tr_values[i-1]) / ATR_Period;
        }
    }
    else
    {
        //--- Calculate SMA of True Range exactly like TradingView: atr2 = sma(tr, Periods)
        double tr_sum = 0;
        for(int i = 0; i < ATR_Period; i++)
        {
            // Calculate True Range exactly like TradingView
            double tr1 = high[i] - low[i];
            double tr2 = MathAbs(high[i] - close[i+1]);
            double tr3 = MathAbs(low[i] - close[i+1]);
            double tr = MathMax(tr1, MathMax(tr2, tr3));
            tr_sum += tr;
        }
        atr = tr_sum / ATR_Period;  // Simple Moving Average of True Range
    }

    //--- Calculate source price based on input parameter
    double src = 0;
    switch(Source_Price)
    {
        case PRICE_CLOSE: src = close[0]; break;
        case PRICE_OPEN: src = open[0]; break;
        case PRICE_HIGH: src = high[0]; break;
        case PRICE_LOW: src = low[0]; break;
        case PRICE_MEDIAN: src = (high[0] + low[0]) / 2.0; break;
        case PRICE_TYPICAL: src = (high[0] + low[0] + close[0]) / 3.0; break;
        case PRICE_WEIGHTED: src = (high[0] + low[0] + 2 * close[0]) / 4.0; break;
        default: src = (high[0] + low[0]) / 2.0; break;
    }

    //--- Calculate basic upper and lower bands
    double up = src - (ATR_Multiplier * atr);
    double dn = src + (ATR_Multiplier * atr);

    //--- Apply Supertrend smoothing logic (match TradingView exactly)
    // TradingView: up1 = nz(up[1], up), dn1 = nz(dn[1], dn)
    double up1 = (prev_up == 0) ? up : prev_up;
    double dn1 = (prev_down == 0) ? dn : prev_down;

    // TradingView: up := close[1] > up1 ? math.max(up, up1) : up
    up = (close[1] > up1) ? MathMax(up, up1) : up;
    // TradingView: dn := close[1] < dn1 ? math.min(dn, dn1) : dn
    dn = (close[1] < dn1) ? MathMin(dn, dn1) : dn;

    //--- Store current values
    up_trend[0] = up;
    down_trend[0] = dn;

    //--- Store previous trend BEFORE calculating new trend (TradingView: trend[1])
    previous_trend = current_trend;

    //--- Apply trend logic exactly like TradingView
    // TradingView: trend = 1, trend := nz(trend[1], trend), trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend

    // Initialize trend on first calculation (match TradingView: trend = 1 initially)
    if(current_trend == 0)
    {
        current_trend = 1; // Start bullish like TradingView (trend = 1)
        Print("🔄 Trend initialized to BULLISH (1) - First calculation");
    }
    else
    {
        // CRITICAL FIX: Use PREVIOUS bar's close for trend determination (like TradingView)
        // TradingView uses close[1] for trend decisions, not current close[0]
        double price_for_trend = close[1];  // Use previous bar close, not current

        // Apply exact TradingView trend logic: trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend
        if(current_trend == -1 && price_for_trend > dn1)
        {
            current_trend = 1;
            Print("🔄 Trend change: BEARISH (-1) → BULLISH (1) | Price[1]: ", price_for_trend, " > dn1: ", dn1);
        }
        else if(current_trend == 1 && price_for_trend < up1)
        {
            current_trend = -1;
            Print("🔄 Trend change: BULLISH (1) → BEARISH (-1) | Price[1]: ", price_for_trend, " < up1: ", up1);
        }
        // else trend remains the same (trend := trend)
    }

    //--- Store trend
    trend_buffer[0] = current_trend;

    //--- Set Supertrend values based on trend
    if(current_trend == 1)
    {
        supertrend_up[0] = up;
        supertrend_down[0] = EMPTY_VALUE;
    }
    else
    {
        supertrend_up[0] = EMPTY_VALUE;
        supertrend_down[0] = dn;
    }

    //--- Update previous values for next calculation
    prev_up = up;
    prev_down = dn;

    //--- Draw trend lines if enabled
    if(Show_Trend_Lines)
    {
        DrawTrendLines(up, dn);
    }

    //--- Draw signals if enabled (TradingView style: immediate signal detection)
    if(Show_Buy_Sell_Signals)
    {
        DrawSignals();
    }

    //--- Check for trading signals (simplified TradingView logic)
    CheckTrendChange();

    return true;
}

//+------------------------------------------------------------------+
//| Check for trend change with confirmation                        |
//+------------------------------------------------------------------+
void CheckTrendChangeWithConfirmation()
{
    if(!Wait_For_Candle_Close)
    {
        // Original behavior - immediate trend change
        CheckTrendChange();
        return;
    }

    // Candle confirmation logic - match TradingView signal detection
    bool buy_signal = (current_trend == 1 && previous_trend == -1);
    bool sell_signal = (current_trend == -1 && previous_trend == 1);

    if(buy_signal || sell_signal)
    {
        if(new_bar_formed)
        {
            // New bar formed - check if we should confirm trend change
            if(pending_trend == 0)
            {
                // First detection of trend change
                pending_trend = current_trend;
                confirmation_count = 1;
                Print("Potential trend change detected: ", (current_trend == 1 ? "BUY" : "SELL"),
                      " - Waiting for confirmation (", confirmation_count, "/", Confirmation_Bars, ")");
            }
            else if(pending_trend == current_trend)
            {
                // Trend change confirmed by another bar
                confirmation_count++;
                Print("Trend change confirmation: ", (current_trend == 1 ? "BUY" : "SELL"),
                      " (", confirmation_count, "/", Confirmation_Bars, ")");

                if(confirmation_count >= Confirmation_Bars)
                {
                    // Validate trend reversal before confirming
                    if(ValidateTrendReversal(current_trend))
                    {
                        // Trend change fully confirmed and validated
                        ConfirmTrendChange();
                    }
                    else
                    {
                        // Trend reversal validation failed
                        Print("Trend change rejected - failed validation");
                        CleanupPendingSignals();
                        pending_trend = 0;
                        confirmation_count = 0;
                        current_trend = previous_trend; // Revert trend
                    }
                }
            }
            else
            {
                // Trend change invalidated
                Print("Trend change invalidated - reverting to previous trend");
                pending_trend = 0;
                confirmation_count = 0;
                current_trend = previous_trend; // Revert trend
            }
        }
    }
    else if(pending_trend != 0 && current_trend == previous_trend)
    {
        // Trend change was invalidated
        Print("Pending trend change cancelled");
        pending_trend = 0;
        confirmation_count = 0;
    }
}

//+------------------------------------------------------------------+
//| Original trend change function                                  |
//+------------------------------------------------------------------+
void CheckTrendChange()
{
    // Match TradingView logic exactly: buySignal = trend == 1 and trend[1] == -1
    bool buy_signal = (current_trend == 1 && previous_trend == -1);
    bool sell_signal = (current_trend == -1 && previous_trend == 1);

    if(buy_signal || sell_signal)
    {
        datetime time_current = iTime(Symbol(), PERIOD_CURRENT, 0);

        // Only process trend change if this is a NEW trend change (prevent duplicates)
        if(last_confirmed_trend != current_trend || last_trend_change_time != time_current)
        {
            trend_start_time = TimeCurrent();
            last_signal_time = TimeCurrent();
            last_signal_type = current_trend;

            // Set flag to indicate trend change occurred
            trend_change_confirmed_this_bar = true;

            // Update trend persistence tracking
            last_confirmed_trend = current_trend;
            last_trend_change_time = time_current;
            signal_already_generated = false; // Reset for signal drawing

            // Signal drawing is handled in main DrawSignals() function

            Print("✅ Trend changed from ", previous_trend, " to ", current_trend,
                  " - Signal: ", (buy_signal ? "BUY" : "SELL"), " at ", TimeToString(time_current, TIME_MINUTES));
        }
    }
}

//+------------------------------------------------------------------+
//| Confirm Trend Change                                            |
//+------------------------------------------------------------------+
void ConfirmTrendChange()
{
    trend_start_time = TimeCurrent();
    last_signal_time = TimeCurrent();
    last_signal_type = current_trend;

    // Reset confirmation variables
    pending_trend = 0;
    confirmation_count = 0;

    // Set flag to indicate trend was confirmed this bar
    trend_change_confirmed_this_bar = true;

    Print("✅ TREND CHANGE CONFIRMED: ", (current_trend == 1 ? "BUY" : "SELL"),
          " after ", Confirmation_Bars, " bar(s) confirmation");

    // Signal drawing is handled in main DrawSignals() function

    // Play sound for confirmed trend change
    PlaySound("alert2.wav");
}

//+------------------------------------------------------------------+
//| Check if we should trade on current trend                       |
//+------------------------------------------------------------------+
bool ShouldTradeOnCurrentTrend()
{
    if(!Wait_For_Candle_Close)
    {
        // No confirmation required - trade immediately
        return true;
    }

    if(pending_trend != 0)
    {
        // Trend change is pending confirmation - don't trade yet
        Print("Trading suspended - waiting for trend confirmation (",
              confirmation_count, "/", Confirmation_Bars, ")");
        return false;
    }

    // Trend is confirmed or no change pending
    return true;
}

//+------------------------------------------------------------------+
//| Check if we should draw signals                                 |
//+------------------------------------------------------------------+
bool ShouldDrawSignals()
{
    if(!Wait_For_Candle_Close)
    {
        // No confirmation required - draw signals immediately
        return true;
    }

    if(pending_trend != 0)
    {
        // Trend change is pending confirmation - don't draw signals yet
        return false;
    }

    // Only draw signals for confirmed trends
    return true;
}

//+------------------------------------------------------------------+
//| Clean up pending/unconfirmed signals                           |
//+------------------------------------------------------------------+
void CleanupPendingSignals()
{
    // This function can be used to remove any pending signal objects
    // that were drawn before confirmation was complete
    Print("Cleaning up pending signals...");
}

//+------------------------------------------------------------------+
//| Validate Trend Reversal                                         |
//+------------------------------------------------------------------+
bool ValidateTrendReversal(int new_trend)
{
    if(!Enable_Trend_Reversal_Check)
        return true;

    // Get current and previous candle data
    double current_open = iOpen(Symbol(), PERIOD_CURRENT, 0);
    double current_close = iClose(Symbol(), PERIOD_CURRENT, 0);
    double current_high = iHigh(Symbol(), PERIOD_CURRENT, 0);
    double current_low = iLow(Symbol(), PERIOD_CURRENT, 0);

    double prev_open = iOpen(Symbol(), PERIOD_CURRENT, 1);
    double prev_close = iClose(Symbol(), PERIOD_CURRENT, 1);

    // For buy signal - validate bullish candle characteristics
    if(new_trend == 1)
    {
        bool is_bullish_candle = current_close > current_open;
        bool closes_above_prev = current_close > prev_close;
        bool strong_close = (current_close - current_low) > (current_high - current_close) * 0.6;

        if(is_bullish_candle && closes_above_prev && strong_close)
        {
            Print("✅ Buy trend reversal validated - Strong bullish candle");
            return true;
        }
        else
        {
            Print("⚠️ Buy trend reversal validation failed - Weak bullish signal");
            return false;
        }
    }
    // For sell signal - validate bearish candle characteristics
    else if(new_trend == -1)
    {
        bool is_bearish_candle = current_close < current_open;
        bool closes_below_prev = current_close < prev_close;
        bool strong_close = (current_high - current_close) > (current_close - current_low) * 0.6;

        if(is_bearish_candle && closes_below_prev && strong_close)
        {
            Print("✅ Sell trend reversal validated - Strong bearish candle");
            return true;
        }
        else
        {
            Print("⚠️ Sell trend reversal validation failed - Weak bearish signal");
            return false;
        }
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check Buy Signal                                                |
//+------------------------------------------------------------------+
bool CheckBuySignal()
{
    // TradingView logic: buySignal = trend == 1 and trend[1] == -1
    return (current_trend == 1 && previous_trend == -1);
}

//+------------------------------------------------------------------+
//| Check Sell Signal                                               |
//+------------------------------------------------------------------+
bool CheckSellSignal()
{
    // TradingView logic: sellSignal = trend == -1 and trend[1] == 1
    return (current_trend == -1 && previous_trend == 1);
}

//+------------------------------------------------------------------+
//| Open Buy Order                                                  |
//+------------------------------------------------------------------+
void OpenBuyOrder()
{
    double price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double sl = (Stop_Loss > 0) ? price - (Stop_Loss * Point()) : 0;
    double tp = (Take_Profit > 0) ? price + (Take_Profit * Point()) : 0;

    if(trade.Buy(Lot_Size, Symbol(), price, sl, tp, Trade_Comment))
    {
        Print("Buy order opened at ", price);
    }
    else
    {
        Print("Failed to open buy order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Open Sell Order                                                 |
//+------------------------------------------------------------------+
void OpenSellOrder()
{
    double price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double sl = (Stop_Loss > 0) ? price + (Stop_Loss * Point()) : 0;
    double tp = (Take_Profit > 0) ? price - (Take_Profit * Point()) : 0;

    if(trade.Sell(Lot_Size, Symbol(), price, sl, tp, Trade_Comment))
    {
        Print("Sell order opened at ", price);
    }
    else
    {
        Print("Failed to open sell order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Count Positions                                                 |
//+------------------------------------------------------------------+
int CountPositions(ENUM_POSITION_TYPE type)
{
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == Symbol() &&
           PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
           PositionGetInteger(POSITION_TYPE) == type)
        {
            count++;
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Calculate Profit for Position Type                              |
//+------------------------------------------------------------------+
double CalculateProfit(ENUM_POSITION_TYPE type)
{
    double profit = 0.0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == Symbol() &&
           PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
           PositionGetInteger(POSITION_TYPE) == type)
        {
            profit += PositionGetDouble(POSITION_PROFIT);
        }
    }
    return profit;
}

//+------------------------------------------------------------------+
//| Manage Trailing Stop                                            |
//+------------------------------------------------------------------+
void ManageTrailingStop()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == Symbol() &&
           PositionGetInteger(POSITION_MAGIC) == Magic_Number)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double current_sl = PositionGetDouble(POSITION_SL);
            double current_tp = PositionGetDouble(POSITION_TP);
            double current_price = (pos_type == POSITION_TYPE_BUY) ?
                                   SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                                   SymbolInfoDouble(Symbol(), SYMBOL_ASK);

            double new_sl = 0;
            bool modify = false;
            double min_profit_points = Min_Profit_To_Trail * Point();

            if(pos_type == POSITION_TYPE_BUY)
            {
                //--- Check if position is profitable enough to start trailing
                double profit_points = current_price - open_price;
                if(profit_points >= min_profit_points)
                {
                    //--- Calculate new stop loss
                    new_sl = current_price - (Trailing_Stop_Distance * Point());

                    //--- Only modify if new SL is better than current SL by at least the step amount
                    if(current_sl == 0 || new_sl > current_sl + (Trailing_Stop_Step * Point()))
                    {
                        //--- Ensure new SL is not too close to current price
                        double min_sl = current_price - (Trailing_Stop_Distance * Point());
                        if(new_sl <= min_sl)
                        {
                            new_sl = min_sl;
                            modify = true;
                        }
                    }
                }
            }
            else if(pos_type == POSITION_TYPE_SELL)
            {
                //--- Check if position is profitable enough to start trailing
                double profit_points = open_price - current_price;
                if(profit_points >= min_profit_points)
                {
                    //--- Calculate new stop loss
                    new_sl = current_price + (Trailing_Stop_Distance * Point());

                    //--- Only modify if new SL is better than current SL by at least the step amount
                    if(current_sl == 0 || new_sl < current_sl - (Trailing_Stop_Step * Point()))
                    {
                        //--- Ensure new SL is not too close to current price
                        double max_sl = current_price + (Trailing_Stop_Distance * Point());
                        if(new_sl >= max_sl)
                        {
                            new_sl = max_sl;
                            modify = true;
                        }
                    }
                }
            }

            if(modify)
            {
                if(trade.PositionModify(ticket, new_sl, current_tp))
                {
                    Print("Trailing stop updated for position ", ticket,
                          " Type: ", EnumToString(pos_type),
                          " Old SL: ", current_sl,
                          " New SL: ", new_sl,
                          " Current Price: ", current_price,
                          " Profit: ", (pos_type == POSITION_TYPE_BUY ? current_price - open_price : open_price - current_price) / Point(), " points");
                }
                else
                {
                    Print("Failed to update trailing stop for position ", ticket, " Error: ", GetLastError());
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Validate Trailing Stop Parameters                               |
//+------------------------------------------------------------------+
void ValidateTrailingStopParameters()
{
    Print("=== TRAILING STOP CONFIGURATION ===");
    Print("Trailing Stop Distance: ", Trailing_Stop_Distance, " points");
    Print("Trailing Stop Step: ", Trailing_Stop_Step, " points");
    Print("Minimum Profit to Trail: ", Min_Profit_To_Trail, " points");

    //--- Validate parameters
    if(Trailing_Stop_Distance <= 0)
    {
        Print("WARNING: Trailing Stop Distance must be positive!");
    }

    if(Trailing_Stop_Step <= 0)
    {
        Print("WARNING: Trailing Stop Step must be positive!");
    }

    if(Min_Profit_To_Trail < Trailing_Stop_Distance)
    {
        Print("WARNING: Minimum Profit to Trail should be >= Trailing Stop Distance for optimal performance");
    }

    if(Trailing_Stop_Step > Trailing_Stop_Distance)
    {
        Print("WARNING: Trailing Stop Step should be <= Trailing Stop Distance");
    }

    //--- Display point value for reference
    double point_value = Point();
    Print("Point value for ", Symbol(), ": ", point_value);
    Print("Trailing Stop Distance in price: ", Trailing_Stop_Distance * point_value);
    Print("===================================");
}

//+------------------------------------------------------------------+
//| Check for New Bar Formation                                     |
//+------------------------------------------------------------------+
void CheckNewBar()
{
    datetime current_bar_time = iTime(Symbol(), PERIOD_CURRENT, 0);

    if(last_bar_time != current_bar_time)
    {
        new_bar_formed = true;
        last_bar_time = current_bar_time;

        // Reset trend confirmation flag on new bar
        trend_change_confirmed_this_bar = false;

        // Reset signal generation flag on new bar to allow new signals
        signal_already_generated = false;

        if(Wait_For_Candle_Close)
        {
            Print("New bar formed at ", TimeToString(current_bar_time, TIME_MINUTES),
                  " - Ready for trend confirmation");
        }
    }
    else
    {
        new_bar_formed = false;
    }
}

//+------------------------------------------------------------------+
//| Open Manual Buy Order                                           |
//+------------------------------------------------------------------+
void OpenManualBuyOrder()
{
    double price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double sl = (Stop_Loss > 0) ? price - (Stop_Loss * Point()) : 0;
    double tp = (Take_Profit > 0) ? price + (Take_Profit * Point()) : 0;

    if(trade.Buy(Lot_Size, Symbol(), price, sl, tp, "Manual Buy - " + Trade_Comment))
    {
        Print("Manual buy order opened at ", price, " SL: ", sl, " TP: ", tp);
        PlaySound("alert.wav");
    }
    else
    {
        Print("Failed to open manual buy order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Open Manual Sell Order                                          |
//+------------------------------------------------------------------+
void OpenManualSellOrder()
{
    double price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double sl = (Stop_Loss > 0) ? price + (Stop_Loss * Point()) : 0;
    double tp = (Take_Profit > 0) ? price - (Take_Profit * Point()) : 0;

    if(trade.Sell(Lot_Size, Symbol(), price, sl, tp, "Manual Sell - " + Trade_Comment))
    {
        Print("Manual sell order opened at ", price, " SL: ", sl, " TP: ", tp);
        PlaySound("alert.wav");
    }
    else
    {
        Print("Failed to open manual sell order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Close Buy Positions                                             |
//+------------------------------------------------------------------+
void CloseBuyPositions()
{
    int closed_count = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetSymbol(i) == Symbol() &&
           PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
           PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(trade.PositionClose(ticket))
            {
                closed_count++;
                Print("Closed BUY position ", ticket);
            }
            else
            {
                Print("Failed to close BUY position ", ticket, " Error: ", GetLastError());
            }
        }
    }

    if(closed_count > 0)
    {
        Print("Closed ", closed_count, " BUY positions");
        PlaySound("alert.wav");
    }
    else
    {
        Print("No BUY positions to close");
    }
}

//+------------------------------------------------------------------+
//| Close Sell Positions                                            |
//+------------------------------------------------------------------+
void CloseSellPositions()
{
    int closed_count = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetSymbol(i) == Symbol() &&
           PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
           PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(trade.PositionClose(ticket))
            {
                closed_count++;
                Print("Closed SELL position ", ticket);
            }
            else
            {
                Print("Failed to close SELL position ", ticket, " Error: ", GetLastError());
            }
        }
    }

    if(closed_count > 0)
    {
        Print("Closed ", closed_count, " SELL positions");
        PlaySound("alert.wav");
    }
    else
    {
        Print("No SELL positions to close");
    }
}

//+------------------------------------------------------------------+
//| Toggle Allow Buy Trades                                         |
//+------------------------------------------------------------------+
void ToggleAllowBuyTrades()
{
    Allow_Buy_Trades = !Allow_Buy_Trades;
    Print("Allow Buy Trades: ", Allow_Buy_Trades ? "ENABLED" : "DISABLED");

    // Update button appearance
    UpdateAllowTradeButtons();

    PlaySound("alert.wav");
}

//+------------------------------------------------------------------+
//| Toggle Allow Sell Trades                                        |
//+------------------------------------------------------------------+
void ToggleAllowSellTrades()
{
    Allow_Sell_Trades = !Allow_Sell_Trades;
    Print("Allow Sell Trades: ", Allow_Sell_Trades ? "ENABLED" : "DISABLED");

    // Update button appearance
    UpdateAllowTradeButtons();

    PlaySound("alert.wav");
}

//+------------------------------------------------------------------+
//| Update Allow Trade Buttons                                      |
//+------------------------------------------------------------------+
void UpdateAllowTradeButtons()
{
    // Update Allow Buy button
    ObjectSetInteger(0, "Dashboard_Allow_Buy_Button", OBJPROP_BGCOLOR, Allow_Buy_Trades ? clrGreen : clrGray);
    ObjectSetString(0, "Dashboard_Allow_Buy_Button_Text", OBJPROP_TEXT, Allow_Buy_Trades ? "ALLOW BUY ON" : "ALLOW BUY OFF");

    // Update Allow Sell button
    ObjectSetInteger(0, "Dashboard_Allow_Sell_Button", OBJPROP_BGCOLOR, Allow_Sell_Trades ? clrRed : clrGray);
    ObjectSetString(0, "Dashboard_Allow_Sell_Button_Text", OBJPROP_TEXT, Allow_Sell_Trades ? "ALLOW SELL ON" : "ALLOW SELL OFF");

    ChartRedraw();
}

//+------------------------------------------------------------------+
//| Initialize Scanner Arrays                                        |
//+------------------------------------------------------------------+
void InitializeScannerArrays()
{
    ArraySetAsSeries(scanner_rsi_values, true);
    ArraySetAsSeries(scanner_stoch_values, true);
    ArraySetAsSeries(scanner_cci_values, true);
    ArraySetAsSeries(scanner_adx_values, true);
    ArraySetAsSeries(scanner_ao_values, true);
}

//+------------------------------------------------------------------+
//| Parse Timeframes from Input String                              |
//+------------------------------------------------------------------+
void ParseTimeframes()
{
    string tf_string = Scanner_Timeframes;
    string separator = ",";

    //--- Clear arrays
    ArrayFree(timeframe_list);
    ArrayFree(timeframe_enums);

    //--- Split string
    string parts[];
    int count = StringSplit(tf_string, StringGetCharacter(separator, 0), parts);

    ArrayResize(timeframe_list, count);
    ArrayResize(timeframe_enums, count);

    for(int i = 0; i < count; i++)
    {
        StringTrimLeft(parts[i]);
        StringTrimRight(parts[i]);
        timeframe_list[i] = parts[i];
        timeframe_enums[i] = StringToTimeframe(parts[i]);
    }

    timeframe_count = count;
}

//+------------------------------------------------------------------+
//| Truncate Timeframe Name for Display                             |
//+------------------------------------------------------------------+
string TruncateTimeframeName(int timeframe_index)
{
    if(timeframe_index >= 0 && timeframe_index < timeframe_count)
    {
        string timeframe_string = StringSubstr(EnumToString(timeframe_enums[timeframe_index]), 7);
        return timeframe_string;
    }
    return "";
}

//+------------------------------------------------------------------+
//| Convert String to Timeframe                                     |
//+------------------------------------------------------------------+
ENUM_TIMEFRAMES StringToTimeframe(string tf_str)
{
    if(tf_str == "M1") return PERIOD_M1;
    if(tf_str == "M5") return PERIOD_M5;
    if(tf_str == "M15") return PERIOD_M15;
    if(tf_str == "M20") return PERIOD_M20;
    if(tf_str == "M30") return PERIOD_M30;
    if(tf_str == "H1") return PERIOD_H1;
    if(tf_str == "H2") return PERIOD_H2;
    if(tf_str == "H3") return PERIOD_H3;
    if(tf_str == "H4") return PERIOD_H4;
    if(tf_str == "H8") return PERIOD_H8;
    if(tf_str == "H12") return PERIOD_H12;
    if(tf_str == "D1") return PERIOD_D1;
    if(tf_str == "W1") return PERIOD_W1;
    if(tf_str == "MN1") return PERIOD_MN1;

    return PERIOD_CURRENT;
}

//+------------------------------------------------------------------+
//| Create Dashboard                                                |
//+------------------------------------------------------------------+
void CreateDashboard()
{
    //--- Dashboard background
    string bg_name = "Dashboard_Background";
    if(ObjectCreate(0, bg_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, bg_name, OBJPROP_XDISTANCE, Dashboard_X);
        ObjectSetInteger(0, bg_name, OBJPROP_YDISTANCE, Dashboard_Y);
        ObjectSetInteger(0, bg_name, OBJPROP_XSIZE, Dashboard_Width);
        ObjectSetInteger(0, bg_name, OBJPROP_YSIZE, Dashboard_Height);
        ObjectSetInteger(0, bg_name, OBJPROP_BGCOLOR, Dashboard_Background);
        ObjectSetInteger(0, bg_name, OBJPROP_BORDER_COLOR, Dashboard_Border);
        ObjectSetInteger(0, bg_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, bg_name, OBJPROP_BACK, false);
    }

    //--- Dashboard title
    string title_name = "Dashboard_Title";
    if(ObjectCreate(0, title_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, title_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, title_name, OBJPROP_YDISTANCE, Dashboard_Y + 10);
        ObjectSetString(0, title_name, OBJPROP_TEXT, "📊 SUPERTREND DASHBOARD");
        ObjectSetString(0, title_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, title_name, OBJPROP_FONTSIZE, Dashboard_Font_Size + 1);
        ObjectSetInteger(0, title_name, OBJPROP_COLOR, clrNavy);
        ObjectSetInteger(0, title_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Current trend label
    string trend_name = "Dashboard_Trend";
    if(ObjectCreate(0, trend_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, trend_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, trend_name, OBJPROP_YDISTANCE, Dashboard_Y + 35);
        ObjectSetString(0, trend_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, trend_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, trend_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Trend duration label
    string duration_name = "Dashboard_Duration";
    if(ObjectCreate(0, duration_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, duration_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, duration_name, OBJPROP_YDISTANCE, Dashboard_Y + 55);
        ObjectSetString(0, duration_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, duration_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, duration_name, OBJPROP_COLOR, clrDarkBlue);
        ObjectSetInteger(0, duration_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Last signal label
    string signal_name = "Dashboard_Signal";
    if(ObjectCreate(0, signal_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, signal_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, signal_name, OBJPROP_YDISTANCE, Dashboard_Y + 75);
        ObjectSetString(0, signal_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, signal_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, signal_name, OBJPROP_COLOR, clrDarkBlue);
        ObjectSetInteger(0, signal_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Active trades label
    string trades_name = "Dashboard_Trades";
    if(ObjectCreate(0, trades_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, trades_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, trades_name, OBJPROP_YDISTANCE, Dashboard_Y + 95);
        ObjectSetString(0, trades_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, trades_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, trades_name, OBJPROP_COLOR, clrDarkBlue);
        ObjectSetInteger(0, trades_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- P&L label
    string pl_name = "Dashboard_PL";
    if(ObjectCreate(0, pl_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, pl_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, pl_name, OBJPROP_YDISTANCE, Dashboard_Y + 115);
        ObjectSetString(0, pl_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, pl_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, pl_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Total P&L label
    string total_pl_name = "Dashboard_Total_PL";
    if(ObjectCreate(0, total_pl_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, total_pl_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, total_pl_name, OBJPROP_YDISTANCE, Dashboard_Y + 135);
        ObjectSetString(0, total_pl_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, total_pl_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, total_pl_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Trading Time Status label
    string time_status_name = "Dashboard_Time_Status";
    if(ObjectCreate(0, time_status_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, time_status_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, time_status_name, OBJPROP_YDISTANCE, Dashboard_Y + 155);
        ObjectSetString(0, time_status_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, time_status_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, time_status_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Scanner toggle button
    string scanner_btn_name = "Dashboard_Scanner_Button";
    if(ObjectCreate(0, scanner_btn_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_YDISTANCE, Dashboard_Y + 175);
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_XSIZE, 80);
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_YSIZE, 20);
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_BGCOLOR, scanner_panel_visible ? clrGreen : clrGray);
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_BORDER_COLOR, clrDarkGray);
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_BACK, false);
    }

    //--- Scanner button text
    string scanner_btn_text_name = "Dashboard_Scanner_Button_Text";
    if(ObjectCreate(0, scanner_btn_text_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, scanner_btn_text_name, OBJPROP_XDISTANCE, Dashboard_X + 50);
        ObjectSetInteger(0, scanner_btn_text_name, OBJPROP_YDISTANCE, Dashboard_Y + 185);
        ObjectSetString(0, scanner_btn_text_name, OBJPROP_TEXT, scanner_panel_visible ? "Scanner ON" : "Scanner OFF");
        ObjectSetString(0, scanner_btn_text_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, scanner_btn_text_name, OBJPROP_FONTSIZE, Dashboard_Font_Size - 1);
        ObjectSetInteger(0, scanner_btn_text_name, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, scanner_btn_text_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, scanner_btn_text_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
    }

    //--- Manual Buy button
    string buy_btn_name = "Dashboard_Buy_Button";
    if(ObjectCreate(0, buy_btn_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, buy_btn_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, buy_btn_name, OBJPROP_YDISTANCE, Dashboard_Y + 205);
        ObjectSetInteger(0, buy_btn_name, OBJPROP_XSIZE, 60);
        ObjectSetInteger(0, buy_btn_name, OBJPROP_YSIZE, 20);
        ObjectSetInteger(0, buy_btn_name, OBJPROP_BGCOLOR, clrGreen);
        ObjectSetInteger(0, buy_btn_name, OBJPROP_BORDER_COLOR, clrDarkGreen);
        ObjectSetInteger(0, buy_btn_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, buy_btn_name, OBJPROP_BACK, false);
    }

    //--- Manual Buy button text
    string buy_btn_text_name = "Dashboard_Buy_Button_Text";
    if(ObjectCreate(0, buy_btn_text_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, buy_btn_text_name, OBJPROP_XDISTANCE, Dashboard_X + 40);
        ObjectSetInteger(0, buy_btn_text_name, OBJPROP_YDISTANCE, Dashboard_Y + 215);
        ObjectSetString(0, buy_btn_text_name, OBJPROP_TEXT, "BUY");
        ObjectSetString(0, buy_btn_text_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, buy_btn_text_name, OBJPROP_FONTSIZE, Dashboard_Font_Size - 1);
        ObjectSetInteger(0, buy_btn_text_name, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, buy_btn_text_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, buy_btn_text_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
    }

    //--- Manual Sell button
    string sell_btn_name = "Dashboard_Sell_Button";
    if(ObjectCreate(0, sell_btn_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, sell_btn_name, OBJPROP_XDISTANCE, Dashboard_X + 80);
        ObjectSetInteger(0, sell_btn_name, OBJPROP_YDISTANCE, Dashboard_Y + 205);
        ObjectSetInteger(0, sell_btn_name, OBJPROP_XSIZE, 60);
        ObjectSetInteger(0, sell_btn_name, OBJPROP_YSIZE, 20);
        ObjectSetInteger(0, sell_btn_name, OBJPROP_BGCOLOR, clrRed);
        ObjectSetInteger(0, sell_btn_name, OBJPROP_BORDER_COLOR, clrDarkRed);
        ObjectSetInteger(0, sell_btn_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, sell_btn_name, OBJPROP_BACK, false);
    }

    //--- Manual Sell button text
    string sell_btn_text_name = "Dashboard_Sell_Button_Text";
    if(ObjectCreate(0, sell_btn_text_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, sell_btn_text_name, OBJPROP_XDISTANCE, Dashboard_X + 110);
        ObjectSetInteger(0, sell_btn_text_name, OBJPROP_YDISTANCE, Dashboard_Y + 215);
        ObjectSetString(0, sell_btn_text_name, OBJPROP_TEXT, "SELL");
        ObjectSetString(0, sell_btn_text_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, sell_btn_text_name, OBJPROP_FONTSIZE, Dashboard_Font_Size - 1);
        ObjectSetInteger(0, sell_btn_text_name, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, sell_btn_text_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, sell_btn_text_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
    }

    //--- Close Buy button
    string close_buy_btn_name = "Dashboard_Close_Buy_Button";
    if(ObjectCreate(0, close_buy_btn_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, close_buy_btn_name, OBJPROP_XDISTANCE, Dashboard_X + 150);
        ObjectSetInteger(0, close_buy_btn_name, OBJPROP_YDISTANCE, Dashboard_Y + 205);
        ObjectSetInteger(0, close_buy_btn_name, OBJPROP_XSIZE, 70);
        ObjectSetInteger(0, close_buy_btn_name, OBJPROP_YSIZE, 20);
        ObjectSetInteger(0, close_buy_btn_name, OBJPROP_BGCOLOR, clrOrange);
        ObjectSetInteger(0, close_buy_btn_name, OBJPROP_BORDER_COLOR, clrDarkOrange);
        ObjectSetInteger(0, close_buy_btn_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, close_buy_btn_name, OBJPROP_BACK, false);
    }

    //--- Close Buy button text
    string close_buy_btn_text_name = "Dashboard_Close_Buy_Button_Text";
    if(ObjectCreate(0, close_buy_btn_text_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, close_buy_btn_text_name, OBJPROP_XDISTANCE, Dashboard_X + 185);
        ObjectSetInteger(0, close_buy_btn_text_name, OBJPROP_YDISTANCE, Dashboard_Y + 215);
        ObjectSetString(0, close_buy_btn_text_name, OBJPROP_TEXT, "CLOSE BUY");
        ObjectSetString(0, close_buy_btn_text_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, close_buy_btn_text_name, OBJPROP_FONTSIZE, Dashboard_Font_Size - 1);
        ObjectSetInteger(0, close_buy_btn_text_name, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, close_buy_btn_text_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, close_buy_btn_text_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
    }

    //--- Close Sell button
    string close_sell_btn_name = "Dashboard_Close_Sell_Button";
    if(ObjectCreate(0, close_sell_btn_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, close_sell_btn_name, OBJPROP_XDISTANCE, Dashboard_X + 230);
        ObjectSetInteger(0, close_sell_btn_name, OBJPROP_YDISTANCE, Dashboard_Y + 205);
        ObjectSetInteger(0, close_sell_btn_name, OBJPROP_XSIZE, 70);
        ObjectSetInteger(0, close_sell_btn_name, OBJPROP_YSIZE, 20);
        ObjectSetInteger(0, close_sell_btn_name, OBJPROP_BGCOLOR, clrOrange);
        ObjectSetInteger(0, close_sell_btn_name, OBJPROP_BORDER_COLOR, clrDarkOrange);
        ObjectSetInteger(0, close_sell_btn_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, close_sell_btn_name, OBJPROP_BACK, false);
    }

    //--- Close Sell button text
    string close_sell_btn_text_name = "Dashboard_Close_Sell_Button_Text";
    if(ObjectCreate(0, close_sell_btn_text_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, close_sell_btn_text_name, OBJPROP_XDISTANCE, Dashboard_X + 265);
        ObjectSetInteger(0, close_sell_btn_text_name, OBJPROP_YDISTANCE, Dashboard_Y + 215);
        ObjectSetString(0, close_sell_btn_text_name, OBJPROP_TEXT, "CLOSE SELL");
        ObjectSetString(0, close_sell_btn_text_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, close_sell_btn_text_name, OBJPROP_FONTSIZE, Dashboard_Font_Size - 1);
        ObjectSetInteger(0, close_sell_btn_text_name, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, close_sell_btn_text_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, close_sell_btn_text_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
    }

    //--- Allow Buy button
    string allow_buy_btn_name = "Dashboard_Allow_Buy_Button";
    if(ObjectCreate(0, allow_buy_btn_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, allow_buy_btn_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, allow_buy_btn_name, OBJPROP_YDISTANCE, Dashboard_Y + 235);
        ObjectSetInteger(0, allow_buy_btn_name, OBJPROP_XSIZE, 90);
        ObjectSetInteger(0, allow_buy_btn_name, OBJPROP_YSIZE, 20);
        ObjectSetInteger(0, allow_buy_btn_name, OBJPROP_BGCOLOR, Allow_Buy_Trades ? clrGreen : clrGray);
        ObjectSetInteger(0, allow_buy_btn_name, OBJPROP_BORDER_COLOR, clrDarkGray);
        ObjectSetInteger(0, allow_buy_btn_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, allow_buy_btn_name, OBJPROP_BACK, false);
    }

    //--- Allow Buy button text
    string allow_buy_btn_text_name = "Dashboard_Allow_Buy_Button_Text";
    if(ObjectCreate(0, allow_buy_btn_text_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, allow_buy_btn_text_name, OBJPROP_XDISTANCE, Dashboard_X + 55);
        ObjectSetInteger(0, allow_buy_btn_text_name, OBJPROP_YDISTANCE, Dashboard_Y + 245);
        ObjectSetString(0, allow_buy_btn_text_name, OBJPROP_TEXT, Allow_Buy_Trades ? "ALLOW BUY ON" : "ALLOW BUY OFF");
        ObjectSetString(0, allow_buy_btn_text_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, allow_buy_btn_text_name, OBJPROP_FONTSIZE, Dashboard_Font_Size - 1);
        ObjectSetInteger(0, allow_buy_btn_text_name, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, allow_buy_btn_text_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, allow_buy_btn_text_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
    }

    //--- Allow Sell button
    string allow_sell_btn_name = "Dashboard_Allow_Sell_Button";
    if(ObjectCreate(0, allow_sell_btn_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, allow_sell_btn_name, OBJPROP_XDISTANCE, Dashboard_X + 110);
        ObjectSetInteger(0, allow_sell_btn_name, OBJPROP_YDISTANCE, Dashboard_Y + 235);
        ObjectSetInteger(0, allow_sell_btn_name, OBJPROP_XSIZE, 90);
        ObjectSetInteger(0, allow_sell_btn_name, OBJPROP_YSIZE, 20);
        ObjectSetInteger(0, allow_sell_btn_name, OBJPROP_BGCOLOR, Allow_Sell_Trades ? clrRed : clrGray);
        ObjectSetInteger(0, allow_sell_btn_name, OBJPROP_BORDER_COLOR, clrDarkGray);
        ObjectSetInteger(0, allow_sell_btn_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, allow_sell_btn_name, OBJPROP_BACK, false);
    }

    //--- Allow Sell button text
    string allow_sell_btn_text_name = "Dashboard_Allow_Sell_Button_Text";
    if(ObjectCreate(0, allow_sell_btn_text_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, allow_sell_btn_text_name, OBJPROP_XDISTANCE, Dashboard_X + 155);
        ObjectSetInteger(0, allow_sell_btn_text_name, OBJPROP_YDISTANCE, Dashboard_Y + 245);
        ObjectSetString(0, allow_sell_btn_text_name, OBJPROP_TEXT, Allow_Sell_Trades ? "ALLOW SELL ON" : "ALLOW SELL OFF");
        ObjectSetString(0, allow_sell_btn_text_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, allow_sell_btn_text_name, OBJPROP_FONTSIZE, Dashboard_Font_Size - 1);
        ObjectSetInteger(0, allow_sell_btn_text_name, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, allow_sell_btn_text_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, allow_sell_btn_text_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
    }
}

//+------------------------------------------------------------------+
//| Update Dashboard                                                |
//+------------------------------------------------------------------+
void UpdateDashboard()
{
    //--- Update trend display
    string trend_text = "Current Trend: ";
    color trend_color = clrGray;

    if(current_trend == 1)
    {
        trend_text += "🟢 BUY";
        trend_color = clrGreen;
    }
    else if(current_trend == -1)
    {
        trend_text += "🔴 SELL";
        trend_color = clrRed;
    }
    else
    {
        trend_text += "🟡 NEUTRAL";
        trend_color = clrOrange;
    }

    ObjectSetString(0, "Dashboard_Trend", OBJPROP_TEXT, trend_text);
    ObjectSetInteger(0, "Dashboard_Trend", OBJPROP_COLOR, trend_color);

    //--- Update trend duration
    string duration_text = "Trend Duration: " + FormatDuration((int)(TimeCurrent() - trend_start_time));
    ObjectSetString(0, "Dashboard_Duration", OBJPROP_TEXT, duration_text);

    //--- Update last signal
    string signal_text = "Last Signal: ";
    if(last_signal_type == 1)
        signal_text += "BUY at " + TimeToString(last_signal_time, TIME_MINUTES);
    else if(last_signal_type == -1)
        signal_text += "SELL at " + TimeToString(last_signal_time, TIME_MINUTES);
    else
        signal_text += "None";

    ObjectSetString(0, "Dashboard_Signal", OBJPROP_TEXT, signal_text);

    //--- Update active trades
    int buy_count = CountPositions(POSITION_TYPE_BUY);
    int sell_count = CountPositions(POSITION_TYPE_SELL);
    string trades_text = StringFormat("Active Trades: %d Buy | %d Sell", buy_count, sell_count);
    ObjectSetString(0, "Dashboard_Trades", OBJPROP_TEXT, trades_text);

    //--- Update P&L
    double buy_profit = CalculateProfit(POSITION_TYPE_BUY);
    double sell_profit = CalculateProfit(POSITION_TYPE_SELL);
    double total_profit = buy_profit + sell_profit;

    string pl_text = StringFormat("P&L: Buy $%.2f | Sell $%.2f", buy_profit, sell_profit);
    ObjectSetString(0, "Dashboard_PL", OBJPROP_TEXT, pl_text);

    string total_pl_text = StringFormat("Total P&L: $%.2f", total_profit);
    color pl_color = (total_profit >= 0) ? clrGreen : clrRed;
    ObjectSetString(0, "Dashboard_Total_PL", OBJPROP_TEXT, total_pl_text);
    ObjectSetInteger(0, "Dashboard_Total_PL", OBJPROP_COLOR, pl_color);

    //--- Update trading time status
    string time_status_text = "Trading Time: " + GetTradingTimeStatus();
    color time_status_color = is_trading_time_allowed ? clrGreen : clrRed;
    ObjectSetString(0, "Dashboard_Time_Status", OBJPROP_TEXT, time_status_text);
    ObjectSetInteger(0, "Dashboard_Time_Status", OBJPROP_COLOR, time_status_color);

    //--- Update scanner button
    UpdateScannerButton();
}

//+------------------------------------------------------------------+
//| Update Scanner Button                                           |
//+------------------------------------------------------------------+
void UpdateScannerButton()
{
    //--- Update button background color
    color btn_color = scanner_panel_visible ? clrGreen : clrGray;
    ObjectSetInteger(0, "Dashboard_Scanner_Button", OBJPROP_BGCOLOR, btn_color);

    //--- Update button text
    string btn_text = scanner_panel_visible ? "Scanner ON" : "Scanner OFF";
    ObjectSetString(0, "Dashboard_Scanner_Button_Text", OBJPROP_TEXT, btn_text);
}

//+------------------------------------------------------------------+
//| Delete Dashboard                                                |
//+------------------------------------------------------------------+
void DeleteDashboard()
{
    ObjectDelete(0, "Dashboard_Background");
    ObjectDelete(0, "Dashboard_Title");
    ObjectDelete(0, "Dashboard_Trend");
    ObjectDelete(0, "Dashboard_Duration");
    ObjectDelete(0, "Dashboard_Signal");
    ObjectDelete(0, "Dashboard_Trades");
    ObjectDelete(0, "Dashboard_PL");
    ObjectDelete(0, "Dashboard_Total_PL");
    ObjectDelete(0, "Dashboard_Time_Status");
    ObjectDelete(0, "Dashboard_Scanner_Button");
    ObjectDelete(0, "Dashboard_Scanner_Button_Text");
    ObjectDelete(0, "Dashboard_Buy_Button");
    ObjectDelete(0, "Dashboard_Buy_Button_Text");
    ObjectDelete(0, "Dashboard_Sell_Button");
    ObjectDelete(0, "Dashboard_Sell_Button_Text");
    ObjectDelete(0, "Dashboard_Close_Buy_Button");
    ObjectDelete(0, "Dashboard_Close_Buy_Button_Text");
    ObjectDelete(0, "Dashboard_Close_Sell_Button");
    ObjectDelete(0, "Dashboard_Close_Sell_Button_Text");
    ObjectDelete(0, "Dashboard_Allow_Buy_Button");
    ObjectDelete(0, "Dashboard_Allow_Buy_Button_Text");
    ObjectDelete(0, "Dashboard_Allow_Sell_Button");
    ObjectDelete(0, "Dashboard_Allow_Sell_Button_Text");
}

//+------------------------------------------------------------------+
//| Format Duration                                                 |
//+------------------------------------------------------------------+
string FormatDuration(int seconds)
{
    int hours = seconds / 3600;
    int minutes = (seconds % 3600) / 60;

    if(hours > 0)
        return StringFormat("%dh %dm", hours, minutes);
    else
        return StringFormat("%dm", minutes);
}

//+------------------------------------------------------------------+
//| Create Multi-Timeframe Scanner                                  |
//+------------------------------------------------------------------+
void CreateScanner()
{
    if(timeframe_count == 0) return;

    //--- Create main panel
    CreateScannerRectangle(SCANNER_MAIN_PANEL, Scanner_X, Scanner_Y, Scanner_Width, Scanner_Height, Scanner_Background);

    //--- Create header panel
    CreateScannerRectangle(SCANNER_HEADER_PANEL, Scanner_X, Scanner_Y, Scanner_Width, 27, Scanner_Header_Color);

    //--- Create header elements
    CreateScannerLabel(SCANNER_HEADER_ICON, CharToString(91), Scanner_X - 12, Scanner_Y + 14, 18, clrAqua, "Wingdings");
    CreateScannerLabel(SCANNER_HEADER_TEXT, "TimeframeScanner", Scanner_X - 105, Scanner_Y + 12, 13, clrWhite);
    CreateScannerLabel(SCANNER_CLOSE_BUTTON, CharToString('r'), Scanner_X - Scanner_Width + 32, Scanner_Y + 14, 18, clrYellow, "Webdings");

    //--- Create symbol header
    CreateScannerRectangle(SCANNER_SYMBOL_RECTANGLE, Scanner_X - 2, Scanner_Y + 35, SCANNER_WIDTH_TIMEFRAME, SCANNER_HEIGHT_RECTANGLE, clrGray);
    CreateScannerLabel(SCANNER_SYMBOL_TEXT, Symbol(), Scanner_X - 47, Scanner_Y + 45, Scanner_Font_Size, clrWhite);

    //--- Create column headers
    string header_names[] = {"BUY", "SELL", "RSI", "STOCH", "CCI", "ADX", "AO"};
    for(int header_index = 0; header_index < ArraySize(header_names); header_index++)
    {
        int x_offset = (Scanner_X - SCANNER_WIDTH_TIMEFRAME) - (header_index < 2 ? header_index * SCANNER_WIDTH_SIGNAL : 2 * SCANNER_WIDTH_SIGNAL + (header_index - 2) * SCANNER_WIDTH_INDICATOR) + (1 + header_index);
        int width = (header_index < 2 ? SCANNER_WIDTH_SIGNAL : SCANNER_WIDTH_INDICATOR);

        CreateScannerRectangle(SCANNER_HEADER_RECTANGLE + IntegerToString(header_index), x_offset, Scanner_Y + 35, width, SCANNER_HEIGHT_RECTANGLE, clrGray);
        CreateScannerLabel(SCANNER_HEADER_TEXT + IntegerToString(header_index), header_names[header_index], x_offset - width/2, Scanner_Y + 45, Scanner_Font_Size, clrWhite);
    }

    //--- Create timeframe rows
    for(int timeframe_index = 0; timeframe_index < timeframe_count; timeframe_index++)
    {
        CreateScannerTimeframeRow(timeframe_index);
    }
}

//+------------------------------------------------------------------+
//| Create Scanner Rectangle                                         |
//+------------------------------------------------------------------+
bool CreateScannerRectangle(string object_name, int x_distance, int y_distance, int x_size, int y_size, color background_color, color border_color = clrBlack)
{
    ResetLastError();
    if(!ObjectCreate(0, object_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        Print(__FUNCTION__, ": failed to create Rectangle: ERR Code: ", GetLastError());
        return false;
    }

    ObjectSetInteger(0, object_name, OBJPROP_XDISTANCE, x_distance);
    ObjectSetInteger(0, object_name, OBJPROP_YDISTANCE, y_distance);
    ObjectSetInteger(0, object_name, OBJPROP_XSIZE, x_size);
    ObjectSetInteger(0, object_name, OBJPROP_YSIZE, y_size);
    ObjectSetInteger(0, object_name, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
    ObjectSetInteger(0, object_name, OBJPROP_BGCOLOR, background_color);
    ObjectSetInteger(0, object_name, OBJPROP_BORDER_COLOR, border_color);
    ObjectSetInteger(0, object_name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, object_name, OBJPROP_BACK, false);

    return true;
}

//+------------------------------------------------------------------+
//| Create Scanner Label                                             |
//+------------------------------------------------------------------+
bool CreateScannerLabel(string object_name, string text, int x_distance, int y_distance, int font_size = 12, color text_color = clrBlack, string font = "Arial Rounded MT Bold")
{
    ResetLastError();
    if(!ObjectCreate(0, object_name, OBJ_LABEL, 0, 0, 0))
    {
        Print(__FUNCTION__, ": failed to create Label: ERR Code: ", GetLastError());
        return false;
    }

    ObjectSetInteger(0, object_name, OBJPROP_XDISTANCE, x_distance);
    ObjectSetInteger(0, object_name, OBJPROP_YDISTANCE, y_distance);
    ObjectSetInteger(0, object_name, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
    ObjectSetString(0, object_name, OBJPROP_TEXT, text);
    ObjectSetString(0, object_name, OBJPROP_FONT, font);
    ObjectSetInteger(0, object_name, OBJPROP_FONTSIZE, font_size);
    ObjectSetInteger(0, object_name, OBJPROP_COLOR, text_color);
    ObjectSetInteger(0, object_name, OBJPROP_ANCHOR, ANCHOR_CENTER);

    return true;
}

//+------------------------------------------------------------------+
//| Create Scanner Timeframe Row                                    |
//+------------------------------------------------------------------+
void CreateScannerTimeframeRow(int timeframe_index)
{
    //--- Highlight current timeframe
    color timeframe_background = (timeframe_enums[timeframe_index] == Period()) ? clrLimeGreen : clrGray;
    color timeframe_text_color = (timeframe_enums[timeframe_index] == Period()) ? clrBlack : clrWhite;

    int y_pos = (Scanner_Y + 35 + SCANNER_HEIGHT_RECTANGLE) + timeframe_index * SCANNER_HEIGHT_RECTANGLE - (1 + timeframe_index);

    //--- Create timeframe rectangle and label
    CreateScannerRectangle(SCANNER_TIMEFRAME_RECTANGLE + IntegerToString(timeframe_index), Scanner_X - 2, y_pos, SCANNER_WIDTH_TIMEFRAME, SCANNER_HEIGHT_RECTANGLE, timeframe_background);
    CreateScannerLabel(SCANNER_TIMEFRAME_TEXT + IntegerToString(timeframe_index), TruncateTimeframeName(timeframe_index), Scanner_X - 47, y_pos + 10, Scanner_Font_Size, timeframe_text_color);

    //--- Create indicator and signal cells
    string header_names[] = {"BUY", "SELL", "RSI", "STOCH", "CCI", "ADX", "AO"};
    for(int header_index = 0; header_index < ArraySize(header_names); header_index++)
    {
        string cell_rectangle_name, cell_text_name;
        color cell_background = (header_index < 2) ? C'230,230,230' : clrBlack;

        switch(header_index)
        {
            case 0: cell_rectangle_name = SCANNER_BUY_RECTANGLE + IntegerToString(timeframe_index); cell_text_name = SCANNER_BUY_TEXT + IntegerToString(timeframe_index); break;
            case 1: cell_rectangle_name = SCANNER_SELL_RECTANGLE + IntegerToString(timeframe_index); cell_text_name = SCANNER_SELL_TEXT + IntegerToString(timeframe_index); break;
            case 2: cell_rectangle_name = SCANNER_RSI_RECTANGLE + IntegerToString(timeframe_index); cell_text_name = SCANNER_RSI_TEXT + IntegerToString(timeframe_index); break;
            case 3: cell_rectangle_name = SCANNER_STOCH_RECTANGLE + IntegerToString(timeframe_index); cell_text_name = SCANNER_STOCH_TEXT + IntegerToString(timeframe_index); break;
            case 4: cell_rectangle_name = SCANNER_CCI_RECTANGLE + IntegerToString(timeframe_index); cell_text_name = SCANNER_CCI_TEXT + IntegerToString(timeframe_index); break;
            case 5: cell_rectangle_name = SCANNER_ADX_RECTANGLE + IntegerToString(timeframe_index); cell_text_name = SCANNER_ADX_TEXT + IntegerToString(timeframe_index); break;
            case 6: cell_rectangle_name = SCANNER_AO_RECTANGLE + IntegerToString(timeframe_index); cell_text_name = SCANNER_AO_TEXT + IntegerToString(timeframe_index); break;
        }

        int x_offset = (Scanner_X - SCANNER_WIDTH_TIMEFRAME) - (header_index < 2 ? header_index * SCANNER_WIDTH_SIGNAL : 2 * SCANNER_WIDTH_SIGNAL + (header_index - 2) * SCANNER_WIDTH_INDICATOR) + (1 + header_index);
        int width = (header_index < 2 ? SCANNER_WIDTH_SIGNAL : SCANNER_WIDTH_INDICATOR);

        CreateScannerRectangle(cell_rectangle_name, x_offset, y_pos, width, SCANNER_HEIGHT_RECTANGLE, cell_background);
        CreateScannerLabel(cell_text_name, "-/-", x_offset - width/2, y_pos + 10, 10, clrWhite);
    }
}





//+------------------------------------------------------------------+
//| Update Multi-Timeframe Scanner                                  |
//+------------------------------------------------------------------+
void UpdateScanner()
{
    if(!scanner_panel_visible) return;

    for(int timeframe_index = 0; timeframe_index < timeframe_count; timeframe_index++)
    {
        ENUM_TIMEFRAMES tf = timeframe_enums[timeframe_index];

        //--- Initialize indicator handles
        int rsi_handle = iRSI(Symbol(), tf, RSI_Period, PRICE_CLOSE);
        int stoch_handle = iStochastic(Symbol(), tf, Stochastic_K_Period, Stochastic_D_Period, Stochastic_Slowing, MODE_SMA, STO_LOWHIGH);
        int cci_handle = iCCI(Symbol(), tf, CCI_Period, PRICE_TYPICAL);
        int adx_handle = iADX(Symbol(), tf, ADX_Period);
        int ao_handle = iAO(Symbol(), tf);

        //--- Check for valid handles
        if(rsi_handle == INVALID_HANDLE || stoch_handle == INVALID_HANDLE ||
           cci_handle == INVALID_HANDLE || adx_handle == INVALID_HANDLE ||
           ao_handle == INVALID_HANDLE)
        {
            Print("Failed to create indicator handle for timeframe ", TruncateTimeframeName(timeframe_index));
            continue;
        }

        //--- Copy indicator values
        if(CopyBuffer(rsi_handle, 0, 0, 1, scanner_rsi_values) <= 0 ||
           CopyBuffer(stoch_handle, 1, 0, 1, scanner_stoch_values) <= 0 ||
           CopyBuffer(cci_handle, 0, 0, 1, scanner_cci_values) <= 0 ||
           CopyBuffer(adx_handle, 0, 0, 1, scanner_adx_values) <= 0 ||
           CopyBuffer(ao_handle, 0, 0, 1, scanner_ao_values) <= 0)
        {
            Print("Failed to copy buffer for timeframe ", TruncateTimeframeName(timeframe_index));
            IndicatorRelease(rsi_handle);
            IndicatorRelease(stoch_handle);
            IndicatorRelease(cci_handle);
            IndicatorRelease(adx_handle);
            IndicatorRelease(ao_handle);
            continue;
        }

        //--- Update RSI
        color rsi_color = (scanner_rsi_values[0] < RSI_Oversold_Level) ? clrBlue : (scanner_rsi_values[0] > RSI_Overbought_Level) ? clrRed : clrWhite;
        UpdateScannerLabel(SCANNER_RSI_TEXT + IntegerToString(timeframe_index), DoubleToString(scanner_rsi_values[0], 2), rsi_color);

        //--- Update Stochastic
        color stoch_color = (scanner_stoch_values[0] < Stoch_Oversold_Level) ? clrBlue : (scanner_stoch_values[0] > Stoch_Overbought_Level) ? clrRed : clrWhite;
        UpdateScannerLabel(SCANNER_STOCH_TEXT + IntegerToString(timeframe_index), DoubleToString(scanner_stoch_values[0], 2), stoch_color);

        //--- Update CCI
        color cci_color = (scanner_cci_values[0] < CCI_Oversold_Level) ? clrBlue : (scanner_cci_values[0] > CCI_Overbought_Level) ? clrRed : clrWhite;
        UpdateScannerLabel(SCANNER_CCI_TEXT + IntegerToString(timeframe_index), DoubleToString(scanner_cci_values[0], 2), cci_color);

        //--- Update ADX
        color adx_color = (scanner_adx_values[0] > 25) ? clrBlue : clrWhite;
        UpdateScannerLabel(SCANNER_ADX_TEXT + IntegerToString(timeframe_index), DoubleToString(scanner_adx_values[0], 2), adx_color);

        //--- Update AO
        color ao_color = (scanner_ao_values[0] > 0) ? clrGreen : (scanner_ao_values[0] < 0) ? clrRed : clrWhite;
        UpdateScannerLabel(SCANNER_AO_TEXT + IntegerToString(timeframe_index), DoubleToString(scanner_ao_values[0], 2), ao_color);

        //--- Calculate and update Buy/Sell signals
        string buy_signal = CalculateScannerSignalStrength(scanner_rsi_values[0], scanner_stoch_values[0], scanner_cci_values[0], scanner_adx_values[0], scanner_ao_values[0], true);
        string sell_signal = CalculateScannerSignalStrength(scanner_rsi_values[0], scanner_stoch_values[0], scanner_cci_values[0], scanner_adx_values[0], scanner_ao_values[0], false);

        //--- Update Buy signal
        color buy_background = (buy_signal == "Strong Buy") ? clrGreen : (buy_signal == "Buy") ? clrSeaGreen : C'105,105,105';
        UpdateScannerRectangle(SCANNER_BUY_RECTANGLE + IntegerToString(timeframe_index), buy_background);
        UpdateScannerLabel(SCANNER_BUY_TEXT + IntegerToString(timeframe_index), buy_signal, clrWhite);

        //--- Update Sell signal
        color sell_background = (sell_signal == "Strong Sell") ? clrRed : (sell_signal == "Sell") ? clrSalmon : C'105,105,105';
        UpdateScannerRectangle(SCANNER_SELL_RECTANGLE + IntegerToString(timeframe_index), sell_background);
        UpdateScannerLabel(SCANNER_SELL_TEXT + IntegerToString(timeframe_index), sell_signal, clrWhite);

        //--- Release indicator handles
        IndicatorRelease(rsi_handle);
        IndicatorRelease(stoch_handle);
        IndicatorRelease(cci_handle);
        IndicatorRelease(adx_handle);
        IndicatorRelease(ao_handle);
    }
}

//+------------------------------------------------------------------+
//| Calculate Scanner Signal Strength                               |
//+------------------------------------------------------------------+
string CalculateScannerSignalStrength(double rsi, double stochastic, double cci, double adx, double ao, bool is_buy)
{
    int signal_strength = 0;

    if(is_buy && rsi < 40) signal_strength++;
    else if(!is_buy && rsi > 60) signal_strength++;

    if(is_buy && stochastic < 40) signal_strength++;
    else if(!is_buy && stochastic > 60) signal_strength++;

    if(is_buy && cci < -70) signal_strength++;
    else if(!is_buy && cci > 70) signal_strength++;

    if(adx > 40) signal_strength++;

    if(is_buy && ao > 0) signal_strength++;
    else if(!is_buy && ao < 0) signal_strength++;

    if(signal_strength >= 3) return is_buy ? "Strong Buy" : "Strong Sell";
    if(signal_strength >= 2) return is_buy ? "Buy" : "Sell";
    return "Neutral";
}

//+------------------------------------------------------------------+
//| Update Scanner Rectangle                                         |
//+------------------------------------------------------------------+
bool UpdateScannerRectangle(string object_name, color background_color)
{
    int found = ObjectFind(0, object_name);
    if(found < 0)
    {
        ResetLastError();
        Print("UNABLE TO FIND THE RECTANGLE: ", object_name, ". ERR Code: ", GetLastError());
        return false;
    }
    ObjectSetInteger(0, object_name, OBJPROP_BGCOLOR, background_color);
    return true;
}

//+------------------------------------------------------------------+
//| Update Scanner Label                                             |
//+------------------------------------------------------------------+
bool UpdateScannerLabel(string object_name, string text, color text_color)
{
    int found = ObjectFind(0, object_name);
    if(found < 0)
    {
        ResetLastError();
        Print("UNABLE TO FIND THE LABEL: ", object_name, ". ERR Code: ", GetLastError());
        return false;
    }
    ObjectSetString(0, object_name, OBJPROP_TEXT, text);
    ObjectSetInteger(0, object_name, OBJPROP_COLOR, text_color);
    return true;
}





//+------------------------------------------------------------------+
//| Delete Scanner                                                  |
//+------------------------------------------------------------------+
void DeleteScanner()
{
    ObjectDelete(0, SCANNER_MAIN_PANEL);
    ObjectDelete(0, SCANNER_HEADER_PANEL);
    ObjectDelete(0, SCANNER_HEADER_ICON);
    ObjectDelete(0, SCANNER_HEADER_TEXT);
    ObjectDelete(0, SCANNER_CLOSE_BUTTON);

    ObjectsDeleteAll(0, SCANNER_SYMBOL_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_SYMBOL_TEXT);
    ObjectsDeleteAll(0, SCANNER_TIMEFRAME_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_TIMEFRAME_TEXT);
    ObjectsDeleteAll(0, SCANNER_HEADER_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_HEADER_TEXT);
    ObjectsDeleteAll(0, SCANNER_RSI_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_RSI_TEXT);
    ObjectsDeleteAll(0, SCANNER_STOCH_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_STOCH_TEXT);
    ObjectsDeleteAll(0, SCANNER_CCI_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_CCI_TEXT);
    ObjectsDeleteAll(0, SCANNER_ADX_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_ADX_TEXT);
    ObjectsDeleteAll(0, SCANNER_AO_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_AO_TEXT);
    ObjectsDeleteAll(0, SCANNER_BUY_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_BUY_TEXT);
    ObjectsDeleteAll(0, SCANNER_SELL_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_SELL_TEXT);

    ChartRedraw();
}

//+------------------------------------------------------------------+
//| Draw Trend Lines                                               |
//+------------------------------------------------------------------+
void DrawTrendLines(double up_value, double dn_value)
{
    if(!Show_Trend_Lines) return;

    datetime time_current = iTime(Symbol(), PERIOD_CURRENT, 0);
    datetime time_prev = iTime(Symbol(), PERIOD_CURRENT, 1);

    //--- Draw continuous Supertrend line
    if(current_trend == 1)
    {
        // Draw green line for bullish trend
        string up_line_name = "Supertrend_Line_" + IntegerToString(time_current);
        if(ObjectCreate(0, up_line_name, OBJ_TREND, 0, time_prev, up_value, time_current, up_value))
        {
            ObjectSetInteger(0, up_line_name, OBJPROP_COLOR, clrLime);
            ObjectSetInteger(0, up_line_name, OBJPROP_WIDTH, Trend_Line_Width);
            ObjectSetInteger(0, up_line_name, OBJPROP_RAY_RIGHT, false);
            ObjectSetInteger(0, up_line_name, OBJPROP_STYLE, STYLE_SOLID);
        }
    }
    else if(current_trend == -1)
    {
        // Draw red line for bearish trend
        string dn_line_name = "Supertrend_Line_" + IntegerToString(time_current);
        if(ObjectCreate(0, dn_line_name, OBJ_TREND, 0, time_prev, dn_value, time_current, dn_value))
        {
            ObjectSetInteger(0, dn_line_name, OBJPROP_COLOR, clrRed);
            ObjectSetInteger(0, dn_line_name, OBJPROP_WIDTH, Trend_Line_Width);
            ObjectSetInteger(0, dn_line_name, OBJPROP_RAY_RIGHT, false);
            ObjectSetInteger(0, dn_line_name, OBJPROP_STYLE, STYLE_SOLID);
        }
    }
}

//+------------------------------------------------------------------+
//| Draw Signals                                                   |
//+------------------------------------------------------------------+
void DrawSignals()
{
    if(!Show_Buy_Sell_Signals) return;

    datetime time_current = iTime(Symbol(), PERIOD_CURRENT, 0);

    // Draw signals based on current trend and confirmation status
    // This matches TradingView's approach: buySignal = trend == 1 and trend[1] == -1
    // IMPORTANT: Only draw signals ONCE per trend change to match TradingView behavior

    //--- Check for trend change (TradingView logic: buySignal = trend == 1 and trend[1] == -1)
    bool buy_signal = (current_trend == 1 && previous_trend == -1);
    bool sell_signal = (current_trend == -1 && previous_trend == 1);

    //--- Draw buy signal (trend changed from -1 to 1)
    if(buy_signal)
    {
        // Check if signal already exists for this bar (prevent duplicates on EA restart)
        string existing_signal = "Buy_Signal_Arrow_" + IntegerToString(time_current);
        bool signal_exists = ObjectFind(0, existing_signal) >= 0;

        // Only draw signal if this is a NEW trend change and signal doesn't already exist
        if(!signal_exists && (last_confirmed_trend != current_trend || last_trend_change_time != time_current))
        {
            DrawBuySignal(time_current);
            last_drawn_signal_time = time_current;
            last_drawn_signal_type = current_trend;
            last_confirmed_trend = current_trend;
            last_trend_change_time = time_current;
            signal_already_generated = true;

            Print("🟢 BUY signal drawn at ", TimeToString(time_current, TIME_MINUTES),
                  " | Current Trend: ", current_trend, " | Previous Trend: ", previous_trend);
        }
        else if(signal_exists)
        {
            // Signal already exists - update tracking variables to match
            last_confirmed_trend = current_trend;
            last_trend_change_time = time_current;
            signal_already_generated = true;
        }
    }

    //--- Draw sell signal (trend changed from 1 to -1)
    if(sell_signal)
    {
        // Check if signal already exists for this bar (prevent duplicates on EA restart)
        string existing_signal = "Sell_Signal_Arrow_" + IntegerToString(time_current);
        bool signal_exists = ObjectFind(0, existing_signal) >= 0;

        // Only draw signal if this is a NEW trend change and signal doesn't already exist
        if(!signal_exists && (last_confirmed_trend != current_trend || last_trend_change_time != time_current))
        {
            DrawSellSignal(time_current);
            last_drawn_signal_time = time_current;
            last_drawn_signal_type = current_trend;
            last_confirmed_trend = current_trend;
            last_trend_change_time = time_current;
            signal_already_generated = true;

            Print("🔴 SELL signal drawn at ", TimeToString(time_current, TIME_MINUTES),
                  " | Current Trend: ", current_trend, " | Previous Trend: ", previous_trend);
        }
        else if(signal_exists)
        {
            // Signal already exists - update tracking variables to match
            last_confirmed_trend = current_trend;
            last_trend_change_time = time_current;
            signal_already_generated = true;
        }
    }
}

//+------------------------------------------------------------------+
//| Draw Buy Signal with Label                                      |
//+------------------------------------------------------------------+
void DrawBuySignal(datetime signal_time)
{
    double low_price = iLow(Symbol(), PERIOD_CURRENT, 0);
    double signal_price = low_price - (Signal_Distance_Points * Point()); // Position below candle

    //--- Create buy arrow
    string buy_arrow_name = "Buy_Signal_Arrow_" + IntegerToString(signal_time);
    if(ObjectCreate(0, buy_arrow_name, OBJ_ARROW_UP, 0, signal_time, signal_price))
    {
        ObjectSetInteger(0, buy_arrow_name, OBJPROP_COLOR, Buy_Signal_Color);
        ObjectSetInteger(0, buy_arrow_name, OBJPROP_WIDTH, Signal_Arrow_Size);
        ObjectSetInteger(0, buy_arrow_name, OBJPROP_ARROWCODE, 233); // Up arrow
    }

    //--- Create buy label (if enabled)
    if(Show_Signal_Labels)
    {
        string buy_label_name = "Buy_Signal_Label_" + IntegerToString(signal_time);
        if(ObjectCreate(0, buy_label_name, OBJ_TEXT, 0, signal_time, signal_price - (30 * Point())))
        {
            ObjectSetString(0, buy_label_name, OBJPROP_TEXT, "BUY");
            ObjectSetString(0, buy_label_name, OBJPROP_FONT, "Arial Bold");
            ObjectSetInteger(0, buy_label_name, OBJPROP_FONTSIZE, Signal_Label_Size);
            ObjectSetInteger(0, buy_label_name, OBJPROP_COLOR, clrWhite);
            ObjectSetInteger(0, buy_label_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
        }

        //--- Create background rectangle for label (if enabled)
        if(Show_Signal_Background)
        {
            string buy_bg_name = "Buy_Signal_BG_" + IntegerToString(signal_time);
            if(ObjectCreate(0, buy_bg_name, OBJ_RECTANGLE, 0, signal_time - 300, signal_price - (Signal_Distance_Points * 2 * Point()),
                            signal_time + 300, signal_price - (Signal_Distance_Points * Point())))
            {
                ObjectSetInteger(0, buy_bg_name, OBJPROP_COLOR, Buy_Signal_Color);
                ObjectSetInteger(0, buy_bg_name, OBJPROP_BGCOLOR, Buy_Signal_Color);
                ObjectSetInteger(0, buy_bg_name, OBJPROP_BACK, true);
                ObjectSetInteger(0, buy_bg_name, OBJPROP_FILL, true);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Draw Sell Signal with Label                                     |
//+------------------------------------------------------------------+
void DrawSellSignal(datetime signal_time)
{
    double high_price = iHigh(Symbol(), PERIOD_CURRENT, 0);
    double signal_price = high_price + (Signal_Distance_Points * Point()); // Position above candle

    //--- Create sell arrow
    string sell_arrow_name = "Sell_Signal_Arrow_" + IntegerToString(signal_time);
    if(ObjectCreate(0, sell_arrow_name, OBJ_ARROW_DOWN, 0, signal_time, signal_price))
    {
        ObjectSetInteger(0, sell_arrow_name, OBJPROP_COLOR, Sell_Signal_Color);
        ObjectSetInteger(0, sell_arrow_name, OBJPROP_WIDTH, Signal_Arrow_Size);
        ObjectSetInteger(0, sell_arrow_name, OBJPROP_ARROWCODE, 234); // Down arrow
    }

    //--- Create sell label (if enabled)
    if(Show_Signal_Labels)
    {
        string sell_label_name = "Sell_Signal_Label_" + IntegerToString(signal_time);
        if(ObjectCreate(0, sell_label_name, OBJ_TEXT, 0, signal_time, signal_price + (30 * Point())))
        {
            ObjectSetString(0, sell_label_name, OBJPROP_TEXT, "SELL");
            ObjectSetString(0, sell_label_name, OBJPROP_FONT, "Arial Bold");
            ObjectSetInteger(0, sell_label_name, OBJPROP_FONTSIZE, Signal_Label_Size);
            ObjectSetInteger(0, sell_label_name, OBJPROP_COLOR, clrWhite);
            ObjectSetInteger(0, sell_label_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
        }

        //--- Create background rectangle for label (if enabled)
        if(Show_Signal_Background)
        {
            string sell_bg_name = "Sell_Signal_BG_" + IntegerToString(signal_time);
            if(ObjectCreate(0, sell_bg_name, OBJ_RECTANGLE, 0, signal_time - 300, signal_price + (Signal_Distance_Points * Point()),
                            signal_time + 300, signal_price + (Signal_Distance_Points * 2 * Point())))
            {
                ObjectSetInteger(0, sell_bg_name, OBJPROP_COLOR, Sell_Signal_Color);
                ObjectSetInteger(0, sell_bg_name, OBJPROP_BGCOLOR, Sell_Signal_Color);
                ObjectSetInteger(0, sell_bg_name, OBJPROP_BACK, true);
                ObjectSetInteger(0, sell_bg_name, OBJPROP_FILL, true);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Chart Event Handler                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long& lparam, const double& dparam, const string& sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == SCANNER_CLOSE_BUTTON)
        {
            Print("Closing the scanner panel now");
            PlaySound("alert.wav");
            scanner_panel_visible = false;
            DeleteScanner();
            UpdateScannerButton();
            ChartRedraw();
        }
        else if(sparam == "Dashboard_Scanner_Button" || sparam == "Dashboard_Scanner_Button_Text")
        {
            ToggleScanner();
        }
        else if(sparam == "Dashboard_Buy_Button" || sparam == "Dashboard_Buy_Button_Text")
        {
            Print("Manual BUY button clicked");
            OpenManualBuyOrder();
        }
        else if(sparam == "Dashboard_Sell_Button" || sparam == "Dashboard_Sell_Button_Text")
        {
            Print("Manual SELL button clicked");
            OpenManualSellOrder();
        }
        else if(sparam == "Dashboard_Close_Buy_Button" || sparam == "Dashboard_Close_Buy_Button_Text")
        {
            Print("CLOSE BUY button clicked");
            CloseBuyPositions();
        }
        else if(sparam == "Dashboard_Close_Sell_Button" || sparam == "Dashboard_Close_Sell_Button_Text")
        {
            Print("CLOSE SELL button clicked");
            CloseSellPositions();
        }
        else if(sparam == "Dashboard_Allow_Buy_Button" || sparam == "Dashboard_Allow_Buy_Button_Text")
        {
            Print("ALLOW BUY button clicked");
            ToggleAllowBuyTrades();
        }
        else if(sparam == "Dashboard_Allow_Sell_Button" || sparam == "Dashboard_Allow_Sell_Button_Text")
        {
            Print("ALLOW SELL button clicked");
            ToggleAllowSellTrades();
        }
    }
}

//+------------------------------------------------------------------+
//| Toggle Scanner Visibility                                       |
//+------------------------------------------------------------------+
void ToggleScanner()
{
    if(scanner_panel_visible)
    {
        Print("Hiding scanner panel");
        scanner_panel_visible = false;
        DeleteScanner();
    }
    else
    {
        Print("Showing scanner panel");
        scanner_panel_visible = true;
        if(Enable_Scanner)
        {
            CreateScanner();
        }
    }

    UpdateScannerButton();
    PlaySound("alert.wav");
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| Clean up Signal Objects                                         |
//+------------------------------------------------------------------+
void CleanupSignalObjects()
{
    //--- Remove old signal objects to prevent chart clutter
    ObjectsDeleteAll(0, "Buy_Signal_Arrow_");
    ObjectsDeleteAll(0, "Buy_Signal_Label_");
    ObjectsDeleteAll(0, "Buy_Signal_BG_");
    ObjectsDeleteAll(0, "Sell_Signal_Arrow_");
    ObjectsDeleteAll(0, "Sell_Signal_Label_");
    ObjectsDeleteAll(0, "Sell_Signal_BG_");
    ObjectsDeleteAll(0, "Hist_Buy_Signal_Arrow_");
    ObjectsDeleteAll(0, "Hist_Buy_Signal_Label_");
    ObjectsDeleteAll(0, "Hist_Buy_Signal_BG_");
    ObjectsDeleteAll(0, "Hist_Sell_Signal_Arrow_");
    ObjectsDeleteAll(0, "Hist_Sell_Signal_Label_");
    ObjectsDeleteAll(0, "Hist_Sell_Signal_BG_");
    ObjectsDeleteAll(0, "Supertrend_Line_");
}

//+------------------------------------------------------------------+
//| Calculate Historical Signals                                    |
//+------------------------------------------------------------------+
void CalculateHistoricalSignals()
{
    int bars_to_analyze = MathMax(50, MathMin(Historical_Bars, 1000)); // Limit between 50-1000
    int available_bars = iBars(Symbol(), PERIOD_CURRENT);
    if(available_bars < bars_to_analyze)
        bars_to_analyze = available_bars - 1;

    Print("Calculating historical Supertrend signals for ", bars_to_analyze, " bars...");

    //--- Arrays for historical calculation
    double hist_high[], hist_low[], hist_close[];
    double hist_atr[];
    double hist_up_trend[], hist_down_trend[];
    int hist_trend[];

    //--- Set arrays as series
    ArraySetAsSeries(hist_high, true);
    ArraySetAsSeries(hist_low, true);
    ArraySetAsSeries(hist_close, true);
    ArraySetAsSeries(hist_atr, true);
    ArraySetAsSeries(hist_up_trend, true);
    ArraySetAsSeries(hist_down_trend, true);
    ArraySetAsSeries(hist_trend, true);

    //--- Resize arrays
    ArrayResize(hist_high, bars_to_analyze);
    ArrayResize(hist_low, bars_to_analyze);
    ArrayResize(hist_close, bars_to_analyze);
    ArrayResize(hist_atr, bars_to_analyze);
    ArrayResize(hist_up_trend, bars_to_analyze);
    ArrayResize(hist_down_trend, bars_to_analyze);
    ArrayResize(hist_trend, bars_to_analyze);

    //--- Get historical price data
    if(CopyHigh(Symbol(), PERIOD_CURRENT, 0, bars_to_analyze, hist_high) < bars_to_analyze ||
       CopyLow(Symbol(), PERIOD_CURRENT, 0, bars_to_analyze, hist_low) < bars_to_analyze ||
       CopyClose(Symbol(), PERIOD_CURRENT, 0, bars_to_analyze, hist_close) < bars_to_analyze)
    {
        Print("Failed to copy historical price data");
        return;
    }

    //--- Calculate historical ATR manually to match TradingView exactly
    // Always use manual calculation for precision

    if(Use_Smoothed_ATR)
    {
        //--- Calculate Wilder's smoothed ATR for historical data
        // First, calculate True Range for all bars
        double tr_values[];
        ArrayResize(tr_values, bars_to_analyze);

        for(int i = 0; i < bars_to_analyze; i++)
        {
            double tr1 = hist_high[i] - hist_low[i];
            double tr2 = (i < bars_to_analyze - 1) ? MathAbs(hist_high[i] - hist_close[i+1]) : tr1;
            double tr3 = (i < bars_to_analyze - 1) ? MathAbs(hist_low[i] - hist_close[i+1]) : tr1;
            tr_values[i] = MathMax(tr1, MathMax(tr2, tr3));
        }

        // Calculate Wilder's smoothed ATR from oldest to newest
        for(int i = bars_to_analyze - 1; i >= 0; i--)
        {
            if(i >= bars_to_analyze - ATR_Period)
            {
                // Not enough data for ATR calculation
                hist_atr[i] = 0;
                continue;
            }

            if(i == bars_to_analyze - ATR_Period - 1)
            {
                // First ATR value is SMA of first ATR_Period values
                double first_atr = 0;
                for(int j = 0; j < ATR_Period; j++)
                {
                    first_atr += tr_values[i + j + 1];
                }
                hist_atr[i] = first_atr / ATR_Period;
            }
            else
            {
                // Apply Wilder's smoothing: ATR = (Previous_ATR * (n-1) + Current_TR) / n
                hist_atr[i] = (hist_atr[i+1] * (ATR_Period - 1) + tr_values[i]) / ATR_Period;
            }
        }
    }
    else
    {
        //--- Calculate SMA of True Range for each bar (TradingView atr2 equivalent)
        for(int i = 0; i < bars_to_analyze; i++)
        {
            if(i < ATR_Period - 1)
            {
                hist_atr[i] = 0; // Not enough data for calculation
                continue;
            }

            double tr_sum = 0;
            for(int j = 0; j < ATR_Period; j++)
            {
                int bar_index = i + j;
                if(bar_index >= bars_to_analyze) break;

                double tr1 = hist_high[bar_index] - hist_low[bar_index];
                double tr2 = (bar_index + 1 < bars_to_analyze) ? MathAbs(hist_high[bar_index] - hist_close[bar_index + 1]) : tr1;
                double tr3 = (bar_index + 1 < bars_to_analyze) ? MathAbs(hist_low[bar_index] - hist_close[bar_index + 1]) : tr1;
                double tr = MathMax(tr1, MathMax(tr2, tr3));
                tr_sum += tr;
            }
            hist_atr[i] = tr_sum / ATR_Period;
        }
    }

    //--- Calculate historical Supertrend values
    double prev_up_hist = 0;
    double prev_down_hist = 0;
    int prev_trend_hist = 1; // Start with bullish trend

    for(int i = bars_to_analyze - 1; i >= 0; i--)
    {
        //--- Calculate source price
        double src = 0;
        switch(Source_Price)
        {
            case PRICE_CLOSE: src = hist_close[i]; break;
            case PRICE_HIGH: src = hist_high[i]; break;
            case PRICE_LOW: src = hist_low[i]; break;
            case PRICE_MEDIAN: src = (hist_high[i] + hist_low[i]) / 2.0; break;
            case PRICE_TYPICAL: src = (hist_high[i] + hist_low[i] + hist_close[i]) / 3.0; break;
            case PRICE_WEIGHTED: src = (hist_high[i] + hist_low[i] + 2 * hist_close[i]) / 4.0; break;
            default: src = (hist_high[i] + hist_low[i]) / 2.0; break;
        }

        //--- Calculate basic upper and lower bands
        double up = src - (ATR_Multiplier * hist_atr[i]);
        double dn = src + (ATR_Multiplier * hist_atr[i]);

        //--- Apply Supertrend logic (match TradingView exactly)
        // TradingView: up1 = nz(up[1], up), dn1 = nz(dn[1], dn)
        double up1_hist = (prev_up_hist == 0) ? up : prev_up_hist;
        double dn1_hist = (prev_down_hist == 0) ? dn : prev_down_hist;

        if(i < bars_to_analyze - 1) // Not the first bar
        {
            // TradingView: up := close[1] > up1 ? math.max(up, up1) : up
            up = (hist_close[i+1] > up1_hist) ? MathMax(up, up1_hist) : up;
            // TradingView: dn := close[1] < dn1 ? math.min(dn, dn1) : dn
            dn = (hist_close[i+1] < dn1_hist) ? MathMin(dn, dn1_hist) : dn;
        }

        //--- Store values
        hist_up_trend[i] = up;
        hist_down_trend[i] = dn;

        //--- Determine trend (match TradingView logic exactly)
        int current_trend_hist = prev_trend_hist;

        // For the first bar, initialize trend to 1 (bullish) like TradingView
        if(i == bars_to_analyze - 1)
        {
            current_trend_hist = 1; // Start bullish like TradingView
        }
        else
        {
            // Apply trend change logic (TradingView: trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend)
            if(current_trend_hist == -1 && hist_close[i] > dn1_hist)
                current_trend_hist = 1;
            else if(current_trend_hist == 1 && hist_close[i] < up1_hist)
                current_trend_hist = -1;
        }

        hist_trend[i] = current_trend_hist;

        //--- Check for trend change and draw signal (match TradingView logic exactly)
        if(i < bars_to_analyze - 1)
        {
            bool buy_signal_hist = (current_trend_hist == 1 && prev_trend_hist == -1);
            bool sell_signal_hist = (current_trend_hist == -1 && prev_trend_hist == 1);

            if(buy_signal_hist || sell_signal_hist)
            {
                datetime signal_time = iTime(Symbol(), PERIOD_CURRENT, i);

                if(buy_signal_hist) // Buy signal
                {
                    DrawHistoricalBuySignal(signal_time, i);
                }
                else if(sell_signal_hist) // Sell signal
                {
                    DrawHistoricalSellSignal(signal_time, i);
                }
            }
        }

        //--- Update previous values
        prev_up_hist = up;
        prev_down_hist = dn;
        prev_trend_hist = current_trend_hist;
    }

    //--- Display calculation summary
    int signal_count = 0;
    for(int obj = 0; obj < ObjectsTotal(0); obj++)
    {
        string obj_name = ObjectName(0, obj);
        if(StringFind(obj_name, "Hist_Buy_Signal_Arrow_") >= 0 || StringFind(obj_name, "Hist_Sell_Signal_Arrow_") >= 0)
            signal_count++;
    }

    Print("Historical signal calculation completed.");
    Print("Analyzed ", bars_to_analyze, " bars and found ", signal_count, " signals.");

    // Create custom source price string for accurate display
    string source_name = "";
    switch(Source_Price)
    {
        case PRICE_CLOSE: source_name = "PRICE_CLOSE"; break;
        case PRICE_OPEN: source_name = "PRICE_OPEN"; break;
        case PRICE_HIGH: source_name = "PRICE_HIGH"; break;
        case PRICE_LOW: source_name = "PRICE_LOW"; break;
        case PRICE_MEDIAN: source_name = "PRICE_MEDIAN (HL2)"; break;
        case PRICE_TYPICAL: source_name = "PRICE_TYPICAL (HLC3)"; break;
        case PRICE_WEIGHTED: source_name = "PRICE_WEIGHTED (HLCC4)"; break;
        default: source_name = "UNKNOWN"; break;
    }

    Print("Supertrend settings: ATR Period=", ATR_Period, ", Multiplier=", ATR_Multiplier, ", Source=", source_name, " (", (int)Source_Price, ")");
}

//+------------------------------------------------------------------+
//| Draw Historical Buy Signal                                      |
//+------------------------------------------------------------------+
void DrawHistoricalBuySignal(datetime signal_time, int bar_index)
{
    double low_price = iLow(Symbol(), PERIOD_CURRENT, bar_index);
    double signal_price = low_price - (Signal_Distance_Points * Point());

    //--- Create buy arrow
    string buy_arrow_name = "Hist_Buy_Signal_Arrow_" + IntegerToString(signal_time);
    if(ObjectCreate(0, buy_arrow_name, OBJ_ARROW_UP, 0, signal_time, signal_price))
    {
        ObjectSetInteger(0, buy_arrow_name, OBJPROP_COLOR, Buy_Signal_Color);
        ObjectSetInteger(0, buy_arrow_name, OBJPROP_WIDTH, Signal_Arrow_Size);
        ObjectSetInteger(0, buy_arrow_name, OBJPROP_ARROWCODE, 233);
    }

    //--- Create buy label (if enabled)
    if(Show_Signal_Labels)
    {
        string buy_label_name = "Hist_Buy_Signal_Label_" + IntegerToString(signal_time);
        if(ObjectCreate(0, buy_label_name, OBJ_TEXT, 0, signal_time, signal_price - (30 * Point())))
        {
            ObjectSetString(0, buy_label_name, OBJPROP_TEXT, "BUY");
            ObjectSetString(0, buy_label_name, OBJPROP_FONT, "Arial Bold");
            ObjectSetInteger(0, buy_label_name, OBJPROP_FONTSIZE, Signal_Label_Size);
            ObjectSetInteger(0, buy_label_name, OBJPROP_COLOR, clrWhite);
            ObjectSetInteger(0, buy_label_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
        }

        //--- Create background rectangle (if enabled)
        if(Show_Signal_Background)
        {
            string buy_bg_name = "Hist_Buy_Signal_BG_" + IntegerToString(signal_time);
            if(ObjectCreate(0, buy_bg_name, OBJ_RECTANGLE, 0, signal_time - 300, signal_price - (Signal_Distance_Points * 2 * Point()),
                            signal_time + 300, signal_price - (Signal_Distance_Points * Point())))
            {
                ObjectSetInteger(0, buy_bg_name, OBJPROP_COLOR, Buy_Signal_Color);
                ObjectSetInteger(0, buy_bg_name, OBJPROP_BGCOLOR, Buy_Signal_Color);
                ObjectSetInteger(0, buy_bg_name, OBJPROP_BACK, true);
                ObjectSetInteger(0, buy_bg_name, OBJPROP_FILL, true);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Draw Historical Sell Signal                                     |
//+------------------------------------------------------------------+
void DrawHistoricalSellSignal(datetime signal_time, int bar_index)
{
    double high_price = iHigh(Symbol(), PERIOD_CURRENT, bar_index);
    double signal_price = high_price + (Signal_Distance_Points * Point());

    //--- Create sell arrow
    string sell_arrow_name = "Hist_Sell_Signal_Arrow_" + IntegerToString(signal_time);
    if(ObjectCreate(0, sell_arrow_name, OBJ_ARROW_DOWN, 0, signal_time, signal_price))
    {
        ObjectSetInteger(0, sell_arrow_name, OBJPROP_COLOR, Sell_Signal_Color);
        ObjectSetInteger(0, sell_arrow_name, OBJPROP_WIDTH, Signal_Arrow_Size);
        ObjectSetInteger(0, sell_arrow_name, OBJPROP_ARROWCODE, 234);
    }

    //--- Create sell label (if enabled)
    if(Show_Signal_Labels)
    {
        string sell_label_name = "Hist_Sell_Signal_Label_" + IntegerToString(signal_time);
        if(ObjectCreate(0, sell_label_name, OBJ_TEXT, 0, signal_time, signal_price + (30 * Point())))
        {
            ObjectSetString(0, sell_label_name, OBJPROP_TEXT, "SELL");
            ObjectSetString(0, sell_label_name, OBJPROP_FONT, "Arial Bold");
            ObjectSetInteger(0, sell_label_name, OBJPROP_FONTSIZE, Signal_Label_Size);
            ObjectSetInteger(0, sell_label_name, OBJPROP_COLOR, clrWhite);
            ObjectSetInteger(0, sell_label_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
        }

        //--- Create background rectangle (if enabled)
        if(Show_Signal_Background)
        {
            string sell_bg_name = "Hist_Sell_Signal_BG_" + IntegerToString(signal_time);
            if(ObjectCreate(0, sell_bg_name, OBJ_RECTANGLE, 0, signal_time - 300, signal_price + (Signal_Distance_Points * Point()),
                            signal_time + 300, signal_price + (Signal_Distance_Points * 2 * Point())))
            {
                ObjectSetInteger(0, sell_bg_name, OBJPROP_COLOR, Sell_Signal_Color);
                ObjectSetInteger(0, sell_bg_name, OBJPROP_BGCOLOR, Sell_Signal_Color);
                ObjectSetInteger(0, sell_bg_name, OBJPROP_BACK, true);
                ObjectSetInteger(0, sell_bg_name, OBJPROP_FILL, true);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Initialize Trading Time Settings                                |
//+------------------------------------------------------------------+
void InitializeTradingTimeSettings()
{
    //--- Parse time strings for each day
    ParseTimeString(Sunday_Start, Sunday_End, daily_trading_times[0]);
    ParseTimeString(Monday_Start, Monday_End, daily_trading_times[1]);
    ParseTimeString(Tuesday_Start, Tuesday_End, daily_trading_times[2]);
    ParseTimeString(Wednesday_Start, Wednesday_End, daily_trading_times[3]);
    ParseTimeString(Thursday_Start, Thursday_End, daily_trading_times[4]);
    ParseTimeString(Friday_Start, Friday_End, daily_trading_times[5]);
    ParseTimeString(Saturday_Start, Saturday_End, daily_trading_times[6]);

    Print("Trading time settings initialized:");
    string days[] = {"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"};
    for(int i = 0; i < 7; i++)
    {
        Print(days[i], ": ",
              StringFormat("%02d:%02d", daily_trading_times[i].start_hour, daily_trading_times[i].start_minute),
              " - ",
              StringFormat("%02d:%02d", daily_trading_times[i].end_hour, daily_trading_times[i].end_minute));
    }
}

//+------------------------------------------------------------------+
//| Parse Time String (HH:MM format)                                |
//+------------------------------------------------------------------+
void ParseTimeString(string start_time, string end_time, TradingTimeSettings &time_setting)
{
    //--- Parse start time
    string start_parts[];
    if(StringSplit(start_time, ':', start_parts) == 2)
    {
        time_setting.start_hour = (int)StringToInteger(start_parts[0]);
        time_setting.start_minute = (int)StringToInteger(start_parts[1]);
    }
    else
    {
        time_setting.start_hour = 0;
        time_setting.start_minute = 0;
        Print("Invalid start time format: ", start_time, ". Using 00:00");
    }

    //--- Parse end time
    string end_parts[];
    if(StringSplit(end_time, ':', end_parts) == 2)
    {
        time_setting.end_hour = (int)StringToInteger(end_parts[0]);
        time_setting.end_minute = (int)StringToInteger(end_parts[1]);
    }
    else
    {
        time_setting.end_hour = 23;
        time_setting.end_minute = 59;
        Print("Invalid end time format: ", end_time, ". Using 23:59");
    }

    //--- Validate time ranges
    if(time_setting.start_hour < 0 || time_setting.start_hour > 23)
    {
        time_setting.start_hour = 0;
        Print("Invalid start hour. Using 0");
    }
    if(time_setting.start_minute < 0 || time_setting.start_minute > 59)
    {
        time_setting.start_minute = 0;
        Print("Invalid start minute. Using 0");
    }
    if(time_setting.end_hour < 0 || time_setting.end_hour > 23)
    {
        time_setting.end_hour = 23;
        Print("Invalid end hour. Using 23");
    }
    if(time_setting.end_minute < 0 || time_setting.end_minute > 59)
    {
        time_setting.end_minute = 59;
        Print("Invalid end minute. Using 59");
    }
}

//+------------------------------------------------------------------+
//| Check if Current Time is Within Trading Hours                   |
//+------------------------------------------------------------------+
void CheckTradingTime()
{
    datetime current_time = Use_Broker_Time ? TimeCurrent() : TimeGMT();

    // Only check once per minute to optimize performance
    if(current_time - last_time_check < 60)
        return;

    last_time_check = current_time;

    MqlDateTime time_struct;
    TimeToStruct(current_time, time_struct);

    int current_day = time_struct.day_of_week; // 0=Sunday, 1=Monday, ..., 6=Saturday
    int current_hour = time_struct.hour;
    int current_minute = time_struct.min;

    TradingTimeSettings today_times = daily_trading_times[current_day];

    //--- Convert current time to minutes since midnight
    int current_minutes = current_hour * 60 + current_minute;
    int start_minutes = today_times.start_hour * 60 + today_times.start_minute;
    int end_minutes = today_times.end_hour * 60 + today_times.end_minute;

    //--- Check if current time is within trading hours
    bool was_trading_allowed = is_trading_time_allowed;

    if(start_minutes <= end_minutes)
    {
        // Normal case: start and end on same day
        is_trading_time_allowed = (current_minutes >= start_minutes && current_minutes < end_minutes);
    }
    else
    {
        // Overnight trading: end time is next day
        is_trading_time_allowed = (current_minutes >= start_minutes || current_minutes < end_minutes);
    }

    //--- Log trading time status changes
    if(was_trading_allowed != is_trading_time_allowed)
    {
        string status = is_trading_time_allowed ? "ALLOWED" : "RESTRICTED";
        string time_zone = Use_Broker_Time ? "Broker Time" : "GMT";
        Print("Trading time status changed to: ", status,
              " (Current: ", StringFormat("%02d:%02d", current_hour, current_minute),
              " ", time_zone, ")");
    }
}

//+------------------------------------------------------------------+
//| Close All Open Trades                                           |
//+------------------------------------------------------------------+
void CloseAllTrades()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetString(POSITION_SYMBOL) == Symbol() &&
               PositionGetInteger(POSITION_MAGIC) == Magic_Number)
            {
                trade.PositionClose(PositionGetTicket(i));
                Print("Closed position outside trading hours: Ticket #", PositionGetTicket(i));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Get Trading Time Status String                                  |
//+------------------------------------------------------------------+
string GetTradingTimeStatus()
{
    if(!Enable_Trading_Hours)
        return "24/7";

    datetime current_time = Use_Broker_Time ? TimeCurrent() : TimeGMT();
    MqlDateTime time_struct;
    TimeToStruct(current_time, time_struct);

    int current_day = time_struct.day_of_week;
    int current_hour = time_struct.hour;
    int current_minute = time_struct.min;
    int current_minutes = current_hour * 60 + current_minute;

    TradingTimeSettings today_times = daily_trading_times[current_day];
    int start_minutes = today_times.start_hour * 60 + today_times.start_minute;
    int end_minutes = today_times.end_hour * 60 + today_times.end_minute;

    string status = is_trading_time_allowed ? "OPEN" : "CLOSED";
    string next_time = "";

    if(is_trading_time_allowed)
    {
        // Currently in trading hours - show when it ends
        next_time = StringFormat("Until %02d:%02d", today_times.end_hour, today_times.end_minute);
    }
    else
    {
        // Currently outside trading hours - find next opening time
        if(start_minutes <= end_minutes)
        {
            // Normal day schedule
            if(current_minutes < start_minutes)
            {
                // Before start time today
                next_time = StringFormat("From %02d:%02d", today_times.start_hour, today_times.start_minute);
            }
            else
            {
                // After end time today - check next day
                int next_day = (current_day + 1) % 7;
                TradingTimeSettings next_day_times = daily_trading_times[next_day];
                next_time = StringFormat("Tomorrow %02d:%02d", next_day_times.start_hour, next_day_times.start_minute);
            }
        }
        else
        {
            // Overnight schedule
            if(current_minutes < end_minutes)
            {
                // Before end time (early morning)
                next_time = StringFormat("From %02d:%02d", today_times.start_hour, today_times.start_minute);
            }
            else if(current_minutes >= start_minutes)
            {
                // After start time (late evening) - should be trading
                next_time = StringFormat("Until %02d:%02d tomorrow", today_times.end_hour, today_times.end_minute);
            }
            else
            {
                // Between end and start
                next_time = StringFormat("From %02d:%02d", today_times.start_hour, today_times.start_minute);
            }
        }
    }

    return status + " (" + next_time + ")";
}
