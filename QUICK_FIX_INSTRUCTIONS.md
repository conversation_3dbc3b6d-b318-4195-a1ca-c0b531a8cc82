# 🚨 URGENT: Source Price Fix Instructions

## 🔍 **Problem Identified**
Your EA log shows `Source=PRICE_LOW` but it should show `Source=PRICE_MEDIAN (HL2)` to match TradingView.

## ✅ **Solution Applied**
I've added debug output to show exactly what Source_Price value is being loaded.

## 🛠️ **Steps to Fix**

### **Step 1: Recompile EA**
1. Open MetaEditor
2. Open `SupertrendEA.mq5`
3. Press **F7** to compile
4. Check for any errors

### **Step 2: Attach EA with Correct Settings**
1. **Remove current EA** from chart
2. **Attach EA again** using one of these methods:

#### **Method A: Load Preset File (Recommended)**
1. Drag EA to chart
2. In EA settings dialog, click **"Load"** button
3. Navigate to `MQL5/Presets/` folder
4. Select `SupertrendEA.set`
5. Click **"OK"**

#### **Method B: Manual Settings**
1. Drag EA to chart
2. In EA settings, manually set:
   - **Source_Price**: `PRICE_MEDIAN` (should show as "4" or "HL2")
   - **ATR_Period**: 10
   - **ATR_Multiplier**: 3.0
   - **Wait_For_Candle_Close**: false
   - **Enable_Trend_Reversal_Check**: false

### **Step 3: Verify Fix**
After attaching EA, check the log output. You should see:
```
📊 Loaded Source_Price: PRICE_MEDIAN (HL2) (4)
Supertrend settings: ATR Period=10, Multiplier=3.0, Source=PRICE_MEDIAN (HL2) (4)
```

**NOT:**
```
Source=PRICE_LOW
```

## 🎯 **Expected Results**
- ✅ Source price will be PRICE_MEDIAN (HL2)
- ✅ Signals will match TradingView exactly
- ✅ Historical signals will be recalculated correctly
- ✅ Debug output will confirm correct settings

## 🚨 **If Still Shows PRICE_LOW**
If the EA still shows `PRICE_LOW` after following these steps:

1. **Check EA Parameters**: In MT5, right-click EA → "Expert Advisors" → "Properties" → Check Source_Price value
2. **Reset to Defaults**: Remove EA, restart MT5, attach EA with default settings
3. **Manual Override**: Set Source_Price manually to 4 in EA settings

## 📊 **TradingView Sync Verification**
Once fixed, your EA should:
- Use HL2 (High+Low)/2 for calculations
- Generate identical signals to TradingView Supertrend
- Show correct source price in debug output
- Display historical signals that match TradingView chart

The fix is ready - just recompile and attach with correct settings! 🚀
